package cloud.demand.lab.modules.longterm.cos.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * cos中长期模型输入参数
 */
@Data
@ToString
@Table("cos_longterm_predict_input_args")
public class CosLongtermPredictInputArgsDO extends BaseDO {

    /** cos预测任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 策略类型，激进中立保守<br/>Column: [strategy_type] */
    @Column(value = "strategy_type")
    private String strategyType;

    /** 日期范围名称<br/>Column: [date_name] */
    @Column(value = "date_name")
    private String dateName;

    /** 日期开始，含当天<br/>Column: [start_date] */
    @Column(value = "start_date")
    private LocalDate startDate;

    /** 日期结束，含当天<br/>Column: [end_date] */
    @Column(value = "end_date")
    private LocalDate endDate;

    /** 外部净增速，例如0.14是14%，不含百分比<br/>Column: [scale_growth_rate_out] */
    @Column(value = "scale_growth_rate_out")
    private BigDecimal scaleGrowthRateOut;

    /** 内部净增速，例如0.14是14%，不含百分比<br/>Column: [scale_growth_rate_in] */
    @Column(value = "scale_growth_rate_in")
    private BigDecimal scaleGrowthRateIn;

    /** 备注<br/>Column: [note] */
    @Column(value = "note")
    private String note;

}