package cloud.demand.lab.modules.longterm.cos.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InputArgsDTO {

    /**策略类型，详见 StrategyTypeEnum */
    private String strategyType;
    /**时间名称*/
    private String dateName;
    /**开始时间范围，含当天*/
    private String startDate;
    /**结束时间范围，含当天*/
    private String endDate;
    /**内部净增长，不含百分比*/
    private BigDecimal innerScaleGrowthRate;
    /**外部净增长，不含百分比*/
    private BigDecimal outerScaleGrowthRate;
    /**备注*/
    private String note;

    /**大客户未来预测*/
    private List<BigCustomerChangeDTO> bigCustomerForecast;

}
