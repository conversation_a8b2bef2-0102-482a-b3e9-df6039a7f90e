package cloud.demand.lab.modules.longterm.predict.service.impl;

import cloud.demand.lab.common.config.CacheConfiguration.SynchronizedHiSpeedCache1Second;
import cloud.demand.lab.common.config.DBList;
import cloud.demand.lab.common.config.DynamicProperties;
import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.common.utils.EStream;
import cloud.demand.lab.common.utils.ErrorUtil;
import cloud.demand.lab.common.utils.ListUtils2;
import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.common.utils.TimeUtils;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.DO.StaticGinstypeDO;
import cloud.demand.lab.modules.common_dict.service.CvmPlanService;
import cloud.demand.lab.modules.longterm.predict.dto.ZoneRegionInfoDTO;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.DataSplitOrder;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.DataSplitTree;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.DataSplitTreeNode;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.DeviceTypePurchaseDataSplit;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.DeviceTypePurchaseDataSplit.PurchaseRealMonthDetail;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.DeviceTypePurchaseDataSplitOrder;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.IndustryDeptPurchaseDataSplit;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.IndustryDeptPurchaseDataSplitOrder;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.LongtermPredictInputScaleIncreaseDTO;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.ScaleDataSplit;
import cloud.demand.lab.modules.longterm.predict.dto.data_split.ScaleDataSplitOrder;
import cloud.demand.lab.modules.longterm.predict.entity.LPOPSplitIndustryDeptDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigBizRangeTypeRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigCumstomhouseTitleRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigInstanceFamilyRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigInstanceFamilyToDeviceTypeRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigInstanceFamilyToDeviceTypeRateDO.YearMonthItem;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigInstanceFamilyToZoneNameRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictConfigMonthRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputArgsDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputBigCustomerPurchaseDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputInstance2deviceRateDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputPurchaseDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputScaleWithIndustryDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitAdjust13wLogDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitAdjustDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitIndustryDeptDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitIndustryDeptTreeDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseSplitTreeDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputScaleDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputScaleSplitDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputScaleSplitTreeDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputSplitVersionDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO.SplitConfig;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.RateGetter;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitDeviceTypeKeyGetter;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitDeviceTypeKeyGetter.Key;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitInstanceFamilyKeyGetter;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitMonthKeyGetter;
import cloud.demand.lab.modules.longterm.predict.entity.SplitRateKeyGetters.SplitZoneNameKeyGetter;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import cloud.demand.lab.modules.longterm.predict.service.LongtermPredictDictService;
import cloud.demand.lab.modules.longterm.predict.service.SplitService;
import cloud.demand.lab.modules.longterm.predict.service.TransToAnnualService;
import cloud.demand.lab.modules.longterm.predict.service.accessor.DateAccessor;
import cloud.demand.lab.modules.longterm.predict.service.accessor.RegionAccessor;
import cloud.demand.lab.modules.longterm.predict.service.excel.DownloadExcelStyleHandler;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.BizRangeTypeRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.CustomhouseTitleRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyToDeviceTypeRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyToDeviceTypeRateExcelDTO;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.InstanceFamilyToZoneNameRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.ConfigSplitRateItem.MonthRate;
import cloud.demand.lab.modules.longterm.predict.web.entity.InstanceToDeviceItem;
import cloud.demand.lab.modules.longterm.predict.web.entity.InstanceToDeviceItem.InstanceToDeviceItemKey;
import cloud.demand.lab.modules.longterm.predict.web.req.TaskIdAndSplitIdReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.BigCustomerExcelDTO;
import cloud.demand.lab.modules.longterm.predict.web.req.split.ConfigSplitRateDiffReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.CreateSplitVersionReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.InstanceToDeviceReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.QueryDeviceTypeSplitDetailReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.QuerySplitVersionReq;
import cloud.demand.lab.modules.longterm.predict.web.req.split.UpdateSplitRstReq;
import cloud.demand.lab.modules.longterm.predict.web.req.trans_to_annual.DownLoadExcelReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateDiffResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateDiffResp.BizRangeTypeRateDiff;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateDiffResp.CustomhouseTitleRateDiff;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateDiffResp.DiffInfoSetter;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateDiffResp.InstanceFamilyRateDiff;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateDiffResp.InstanceFamilyToDeviceTypeRateDiff;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateDiffResp.InstanceFamilyToZoneNameRateDiff;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateDiffResp.MonthRateDiff;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateDiffResp.YearMonthItemDiff;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ConfigSplitRateResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionResp.Table1;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionResp.Table2;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionResp.Table3;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionResp.Tree1;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionResp.Tree2;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionResp.Tree3;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionTableResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionTableResp.Item1;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionTableResp.Item2;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.CreateSplitVersionTableResp.Item3;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.DateColumList;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.Instance2DeviceRateResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.InstanceToDeviceResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.InstanceToDeviceResp.InstanceToDeviceItemDiff;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.ParseBigCustomInfoExcelResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.QueryDeviceTypeSplitDetailResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.QueryDeviceTypeSplitDetailResp.AdjustItem;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.QueryDeviceTypeSplitDetailResp.Item;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.QueryDeviceTypeSplitDetailResp.StrategyTypeItem;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.QuerySplitVersionResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.split.QuerySplitVersionResp.SplitVersion;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import java.util.Optional;
import java.util.function.Supplier;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.json.Json;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springdoc.webmvc.ui.SwaggerIndexTransformer;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cloud.demand.lab.modules.longterm.predict.service.impl.TransToAnnualServiceImpl.convertToExcelColumn;

@SuppressWarnings("SpringTransactionalComponentInspection")
@Slf4j
@Service
public class SplitServiceImpl implements SplitService {

    @Resource
    private DBHelper cdLabDbHelper;
    @Resource
    SplitServiceImpl self;

    @Resource
    CvmPlanService cvmPlanService;
    @Resource
    RedisHelper redisHelper;
    @Resource
    LongtermPredictDictService longtermPredictDictService;
    @Resource
    TransToAnnualService transToAnnualService;
    @Resource
    private DBQuery dbQuery;

    private static ThreadPoolExecutor updateTaskStatusThreadPool = ThreadPoolUtils.createThreadPool(
            1, 100, 1, "update-split-task-status");
    @Autowired
    private SwaggerIndexTransformer indexPageTransformer;


    @Override
    public QuerySplitVersionResp querySplitVersionList(QuerySplitVersionReq req) {
        Long taskId = req.getTaskId();
        if (taskId == null || taskId <= 0) {
            throw new IllegalArgumentException("taskId不能为空");
        }
        List<LongtermPredictOutputSplitVersionDO> dbSplitVersions = self.getSplitVersions(taskId);

        List<LongtermPredictOutputPurchaseSplitAdjust13wLogDO> remove13weekLogs = cdLabDbHelper.getAll(
                LongtermPredictOutputPurchaseSplitAdjust13wLogDO.class, "where task_id=?", taskId);
        Map<Long, List<LongtermPredictOutputPurchaseSplitAdjust13wLogDO>> remove13weekLogMap = ListUtils.toMapList(
                remove13weekLogs, o -> o.getSplitVersionId(), o -> o);

        QuerySplitVersionResp resp = new QuerySplitVersionResp();
        resp.setSplitVersionList(
                dbSplitVersions.stream().map((dbVersion) -> {
                    SplitVersion splitVersion = new SplitVersion();
                    splitVersion.setSplitVersionName(dbVersion.getName());
                    splitVersion.setNote(dbVersion.getNote());
                    splitVersion.setCreateTime(DateUtils.format(dbVersion.getCreateTime()));
                    splitVersion.setSplitVersionId(dbVersion.getId());
                    splitVersion.setIsRemove13week(
                            dbVersion.getIsRemove13week() != null && dbVersion.getIsRemove13week());
                    splitVersion.setIsTransToAnnual(
                            dbVersion.getIsTransToAnnual() != null && dbVersion.getIsTransToAnnual());
                    splitVersion.setTransToAnnualTime(DateUtils.format(dbVersion.getTransToAnnualTime()));
                    splitVersion.setRemove13weekLogs(remove13weekLogMap.get(dbVersion.getId()));
                    return splitVersion;
                }).collect(Collectors.toList())
        );
        ListUtils.sortDescNullLast(resp.getSplitVersionList(), SplitVersion::getCreateTime);
        return resp;
    }


    @Override
    @Transactional(value = "cdlabTransactionManager")
    public List<IndustryDeptPurchaseDataSplit> splitIndustryDeptPurchase(
            LongtermPredictOutputSplitVersionDO versionDO, CreateSplitVersionReq req) {

        Long taskId = versionDO.getTaskId();
        SplitDependDataDTO splitDependData = getSplitDependData(taskId);
        // 加上4个配置表
        splitDependData.setCustomhouseTitleRate(req.getCustomhouseTitleRate());
        splitDependData.setBizRangeTypeRate(req.getBizRangeTypeRate());
        Map<Long, LongtermPredictInputArgsDO> inputArgsDOMap =
                ListUtils2.toMap(splitDependData.getAllInputArgs(), BaseDO::getId);

        List<IndustryDeptPurchaseDataSplit> trees = Lang.list();
        for (LongtermPredictOutputPurchaseDO originData : splitDependData.getTotalOriginData()) {
            LongtermPredictInputArgsDO oneInputArg = inputArgsDOMap.get(originData.getInputArgsId());

            //v3 新功能： 对于大客户的需求，如果有数据先减去他们的采购量，然后去拆分数据
            HistoryRealInfo historyReal = getHistoryReal(oneInputArg, splitDependData);
            IndustryDeptPurchaseDataSplit tree = new IndustryDeptPurchaseDataSplit("拆分行业部门");
            tree.setSplitDependDataDTO(splitDependData);
            tree.setRealMonthLen(historyReal.getRealMonthLen());
            tree.setRealMonthTotalPurchase(historyReal.getRealMonthTotalPurchase());
            tree.setRealMonthDetail(historyReal.getRealPurchaseDetail());
            DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> node
                    = DataSplitTreeNode.newNode("原始数据", originData.toIndustryDeptSplitDO());
            node.getData().setSplitVersionId(versionDO.getId());
            tree.addNode(tree.getRoot(), node);
            tree.setCurInputArgs(oneInputArg);

            // 按历史存量比例拆分
            tree.getSplitOrder().forEachRemaining((o) -> {
                log.info("开始 {}", o.getName());
                if (Strings.equals(IndustryDeptPurchaseDataSplitOrder.CUSTOM_HOUSE_TITLE_SPLIT, o.getName())) {
                    splitCustomhouseTitleByConfig(tree);
                } else if (Strings.equals(IndustryDeptPurchaseDataSplitOrder.BIZ_RANGE_TYPE_SPLIT, o.getName())) {
                    splitBizRangeTypeByConfig(tree);
                } else if (Strings.equals(IndustryDeptPurchaseDataSplitOrder.YEAR_MONTH, o.getName())) {
                    splitMonthV1(tree);
                } else if (Strings.equals(IndustryDeptPurchaseDataSplitOrder.CUSTOMER_SPLIT, o.getName())) {
                    splitCustomerByConfig(tree);
                } else {
                    splitByOrder(tree);
                }
            });

            setNotSplitColumn(versionDO, tree.getAfterSplitData());
            trees.add(tree);
        }
        return trees;
    }

    private void splitBizRangeTypeByConfig(IndustryDeptPurchaseDataSplit tree) {
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> oneLeaf : tree.getLeafLevel()) {
            LongtermPredictOutputPurchaseSplitIndustryDeptDO needSplit = oneLeaf.getData();
            List<BizRangeTypeRate> dbConfig = tree.getSplitDependDataDTO().getBizRangeTypeRate();
            for (BizRangeTypeRate config : dbConfig) {
                String msg = String.format("拆分%s, %s比例拆分", config.getBizRangeType(), config.getRate());
                LongtermPredictOutputPurchaseSplitIndustryDeptDO clone = JSON.clone(needSplit);
                clone.setBizRangeType(config.getBizRangeType());
                BigDecimal newPurchaseCore = needSplit.getPurchaseCore().multiply(config.getRate());
                clone.setPurchaseCore(newPurchaseCore);
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> node
                        = DataSplitTreeNode.newNode(msg, clone);
                tree.addNode(oneLeaf, node);
            }
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), needSplit.getPurchaseCore());
        }
    }

    private void splitCustomhouseTitleByConfig(IndustryDeptPurchaseDataSplit tree) {
        LongtermPredictTaskDO taskDO = tree.getSplitDependDataDTO().getTaskDO();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
        Map<String, String> countryNameInfoMap = EStream.of(regionNameInfoMap.values())
                .toMap(SoeRegionNameCountryDO::getCountryName, SoeRegionNameCountryDO::getCustomhouseTitle,
                        (a, b) -> a);
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> oneLeaf : tree.getLeafLevel()) {
            LongtermPredictOutputPurchaseSplitIndustryDeptDO needSplit = oneLeaf.getData();
            if (taskDO.isDecisionCategory()) { // 只关联境内的数据
                setCustomhouseTitle(needSplit, regionNameInfoMap, countryNameInfoMap);
                continue;
            }
            List<CustomhouseTitleRate> dbConfig = tree.getSplitDependDataDTO().getCustomhouseTitleRate();
            for (CustomhouseTitleRate config : dbConfig) {
                String msg = String.format("拆分%s, %s比例拆分", config.getCustomhouseTitle(), config.getRate());
                LongtermPredictOutputPurchaseSplitIndustryDeptDO clone = JSON.clone(needSplit);
                clone.setCustomhouseTitle(config.getCustomhouseTitle());
                BigDecimal newPurchaseCore = needSplit.getPurchaseCore().multiply(config.getRate());
                clone.setPurchaseCore(newPurchaseCore);
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> node
                        = DataSplitTreeNode.newNode(msg, clone);
                IndustryScaleSplitInfo industryScaleSplitInfo = new IndustryScaleSplitInfo();
                industryScaleSplitInfo.setRate(config.getRate());
                industryScaleSplitInfo.setKey(config.getCustomhouseTitle());
                node.setSplitInfo(industryScaleSplitInfo);
                tree.addNode(oneLeaf, node);
            }
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), needSplit.getPurchaseCore());
        }
    }

    private void setNotSplitColumn(LongtermPredictOutputSplitVersionDO versionDO,
            List<LongtermPredictOutputPurchaseSplitIndustryDeptDO> afterSplitData) {
        afterSplitData.forEach((o) -> {
            o.setPurchaseCoreOrigin(o.getPurchaseCore());
            o.setSplitVersionId(versionDO.getId());
        });
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class HistoryRealInfo {

        Integer realMonthLen = 0;
        BigDecimal realMonthTotalPurchase = BigDecimal.ZERO;
        List<PurchaseRealMonthDetail> realPurchaseDetail = new ArrayList<>();

        // 预测输入数据的最后一个月的数据
        List<PurchaseRealMonthDetail> lastMonthRealPurchaseDetail = new ArrayList<>();
    }

    private HistoryRealInfo getHistoryReal(LongtermPredictInputArgsDO oneInputArg,
            SplitDependDataDTO splitDependData) {
        LongtermPredictTaskDO taskDO = splitDependData.getTaskDO();
        Period period = Period.between(oneInputArg.getStartDate(), taskDO.getPredictStart());

        YearMonth lastMonth = YearMonth.from(taskDO.getPredictStart().minusMonths(1));
        YearMonth lastHalfYear = lastMonth.minusMonths(5);
        List<PurchaseRealMonthDetail> lastHalfYearRealPurchaseDetail
                = self.getLastMonthRealPurchaseDetail(taskDO.getId(),  lastHalfYear, lastMonth);

        int diffMonth = period.getYears() * 12 + period.getMonths();
        if (diffMonth > 0) {
            BigDecimal historyPurchaseCore = self.getRealPurchase(taskDO.getId(),
                    oneInputArg.getStartDate(), taskDO.getPredictStart().withDayOfMonth(1));
            List<PurchaseRealMonthDetail> realPurchaseDetail = self.getRealPurchaseDetail(taskDO.getId(),
                    oneInputArg.getStartDate(), taskDO.getPredictStart().withDayOfMonth(1));

            return new HistoryRealInfo(diffMonth,
                    historyPurchaseCore == null ? BigDecimal.ZERO : historyPurchaseCore,
                    realPurchaseDetail, lastHalfYearRealPurchaseDetail);
        }
        return new HistoryRealInfo(diffMonth,BigDecimal.ZERO,Lang.list(),lastHalfYearRealPurchaseDetail);
    }

    @SynchronizedHiSpeedCache1Second
    @HiSpeedCache(keyScript = "args[0]+args[1]+args[2]", expireSecond = 300)
    public BigDecimal getRealPurchase(Long taskId, LocalDate startDate, LocalDate endDate) {
        String sql = "select sum(purchase_core) as core from longterm_predict_input_purchase\n"
                + "where task_id=? and  stat_time >= ? and stat_time < ?";
        BigDecimal historyPurchaseCore =
                LongtermPredictInputPurchaseDO.db().getDbHelper()
                        .getRawOne(BigDecimal.class, sql, taskId, startDate, endDate);
        return historyPurchaseCore == null ? BigDecimal.ZERO : historyPurchaseCore;
    }

    @SynchronizedHiSpeedCache1Second
    @HiSpeedCache(keyScript = "args[0]+args[1]+args[2]", expireSecond = 300)
    public List<PurchaseRealMonthDetail> getRealPurchaseDetail(Long taskId, LocalDate startDate, LocalDate endDate) {
        String sql = "select region_name,country_name,sum(purchase_core) as core from longterm_predict_input_purchase\n"
                + "where task_id=? and  stat_time >= ? and stat_time < ? group by region_name,country_name";
        return LongtermPredictInputPurchaseDO.db().getDbHelper()
                .getRaw(PurchaseRealMonthDetail.class, sql, taskId, startDate, endDate);
    }

    // 获取历史某一个月的历史数据
    @SynchronizedHiSpeedCache1Second
    @HiSpeedCache(keyScript = "args[0]+args[1]", expireSecond = 300)
    public List<PurchaseRealMonthDetail>
    getLastMonthRealPurchaseDetail(Long taskId, YearMonth begin, YearMonth end) {
        String sql = "select region_name,country_name,sum(purchase_core) as core from longterm_predict_input_purchase\n"
                + "where task_id=? and year_month_str between ? and ? group by region_name,country_name";
        return LongtermPredictInputPurchaseDO.db().getDbHelper()
                .getRaw(PurchaseRealMonthDetail.class, sql, taskId, begin.toString(), end.toString());
    }

    @Override
    @Transactional(value = "cdlabTransactionManager")
    public List<DeviceTypePurchaseDataSplit> splitDevicePurchase(
            LongtermPredictOutputSplitVersionDO versionDO, CreateSplitVersionReq req) {

        List<DeviceTypePurchaseDataSplit> trees = Lang.list();

        SplitDependDataDTO splitDependData = getSplitDependData(versionDO.getTaskId());
        // 加上6个配置表
        splitDependData.setCustomhouseTitleRate(req.getCustomhouseTitleRate());
        splitDependData.setBizRangeTypeRate(req.getBizRangeTypeRate());
        splitDependData.setMonthRate(req.getMonthRate());
        splitDependData.setInstanceFamilyRate(req.getInstanceFamilyRate());
        splitDependData.setInstanceFamilyToDeviceTypeRate(req.getInstanceFamilyToDeviceTypeRate());
        splitDependData.setInstanceFamilyToZoneNameRate(req.getInstanceFamilyToZoneNameRate());

        Map<Long, LongtermPredictInputArgsDO> inputArgsDOMap =
                ListUtils2.toMap(splitDependData.getAllInputArgs(), BaseDO::getId);

        LocalDate predictStart = splitDependData.getTaskDO().getPredictStart();
        // 保存大客户的数据，获取历史数据
        List<LongtermPredictInputBigCustomerPurchaseDO> allBigCustomerPurchase =
                saveBigCustomerPurchaseToDb(versionDO, req, predictStart);
        splitDependData.setAllBigCustomerPurchase(allBigCustomerPurchase);

        for (LongtermPredictOutputPurchaseDO originData : splitDependData.getTotalOriginData()) {
            LongtermPredictInputArgsDO oneInputArg = inputArgsDOMap.get(originData.getInputArgsId());
            // 1. 查询需要拆分的数据历史数据
            HistoryRealInfo historyReal = getHistoryReal(oneInputArg, splitDependData);

            DeviceTypePurchaseDataSplit tree = new DeviceTypePurchaseDataSplit("拆分物理机机型");
            tree.setSplitDependDataDTO(splitDependData);
            tree.setRealMonthLen(historyReal.getRealMonthLen());
            tree.setRealMonthTotalPurchase(historyReal.getRealMonthTotalPurchase());
            tree.setRealMonthDetail(historyReal.getRealPurchaseDetail());
            tree.setLastMonthRealPurchaseDetail(historyReal.getLastMonthRealPurchaseDetail());
            DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> origin
                    = DataSplitTreeNode.newNode("原始数据", originData.toDeviceTypeSplitDO());
            origin.getData().setSplitVersionId(versionDO.getId());
            tree.addNode(tree.getRoot(), origin);
            tree.setCurInputArgs(oneInputArg);

            tree.getSplitOrder().forEachRemaining((o) -> {
                log.info("开始 {}", o.getName());
                splitByOrder(tree);
            });

            setNoSplitData(versionDO, tree.getAfterSplitData());
            trees.add(tree);
        }
        return trees;
    }

    private List<LongtermPredictInputBigCustomerPurchaseDO> saveBigCustomerPurchaseToDb(
            LongtermPredictOutputSplitVersionDO versionDO, CreateSplitVersionReq req, LocalDate predictStart) {

        EStream.of(req.getBigCustomerForecast())
                .map((o) -> BigCustomerExcelDTO.toDo(o, req.getTaskId(), versionDO.getId(), predictStart))
                .flatMap(Collection::stream)
                .consumeAsList((list) -> cdLabDbHelper.insertBatchWithoutReturnId(list));

        List<LongtermPredictInputBigCustomerPurchaseDO> allBigCustomerPurchase =
                cdLabDbHelper.getAll(LongtermPredictInputBigCustomerPurchaseDO.class,
                        "where split_version_id=?", versionDO.getId());
        return allBigCustomerPurchase;
    }

    private void setNoSplitData(LongtermPredictOutputSplitVersionDO versionDO,
            List<LongtermPredictOutputPurchaseSplitDO> afterSplitData) {
        Map<String, Integer> deviceTypeToCoreNumMap = longtermPredictDictService.getDeviceTypeToCoreNumMap();
        afterSplitData.forEach((o) -> {
            BigDecimal purchaseCore = o.getPurchaseCore();
            o.setPurchaseCoreDebug(purchaseCore);
            Integer deviceTypeCoreNum = deviceTypeToCoreNumMap.getOrDefault(o.getDeviceType(), 0);
            o.setDeviceTypeCoreNum(deviceTypeCoreNum);
            if (deviceTypeCoreNum > 0) {
                BigDecimal newCore =
                        purchaseCore.divide(BigDecimal.valueOf(deviceTypeCoreNum), 0, RoundingMode.HALF_UP);
                o.setPurchaseCore(BigDecimal.valueOf((long) newCore.intValue() * deviceTypeCoreNum));
                o.setPurchaseCoreOrigin(BigDecimal.valueOf((long) newCore.intValue() * deviceTypeCoreNum));
            } else {
                o.setPurchaseCore(BigDecimal.ZERO);
                o.setPurchaseCoreOrigin(BigDecimal.ZERO);
            }
            o.setSplitVersionId(versionDO.getId());
        });
    }

    private SplitDependDataDTO getSplitDependData(Long taskId) {
        LongtermPredictTaskDO taskDO = self.getTask(taskId);
        List<LongtermPredictInputArgsDO> allInputArgs = self.getInputArgs(taskId);

        LocalDate predictStart = taskDO.getPredictStart();
        List<YearMonth> historyDatePoint = getSub1AndSub6ByPredictStart(predictStart);

        List<LongtermPredictInputScaleDO> inputScaleDetail = self.getTaskScale(taskDO.getId(), historyDatePoint);
        inputScaleDetail = inputScaleDetail.stream()
                .filter((o) ->
                        !Lang.list("蜂驰型", "特惠型", "独享型", "批量型", "其他", "FPGA型")
                                .contains(o.getInstanceFamily())
                                && !Lang.list("58同城CDZ北京一区", "清远一区").contains(o.getZoneName())
                ).collect(Collectors.toList());

        List<LongtermPredictOutputPurchaseDO> totalOriginData =
                LongtermPredictOutputPurchaseDO.db().getAll("where task_id=?", taskId);

        List<LongtermPredictInputScaleWithIndustryDO> inputScaleWithIndustry =
                self.getTaskScaleWithIndustry(taskDO.getId(), historyDatePoint);

        String sql = ORMUtils.getSql("/sql/longterm_predict/split/history_increase.sql");
        Map<String, Object> params = new HashMap<>();
        params.put("task_id", taskId);
        params.put("start_date", historyDatePoint.get(1).minusMonths(1).toString());
        params.put("end_date", historyDatePoint.get(0).toString());
        List<LongtermPredictInputScaleIncreaseDTO> increaseDetail =
                cdLabDbHelper.getRaw(LongtermPredictInputScaleIncreaseDTO.class, sql, params);

        SplitDependDataDTO splitDependDataDTO = new SplitDependDataDTO(taskDO, allInputArgs, historyDatePoint,
                inputScaleDetail, totalOriginData);
        splitDependDataDTO.setInputScaleWithIndustry(inputScaleWithIndustry);
        splitDependDataDTO.setIncreaseDetail(increaseDetail);
        return splitDependDataDTO;
    }


    @Data
    public static class SplitDependDataDTO {

        public final LongtermPredictTaskDO taskDO;
        public final List<LongtermPredictInputArgsDO> allInputArgs;
        // 近1个月，和近6个月
        public final List<YearMonth> historyDatePoint;
        // 近1个月，和近6个月的实际规模数据
        public final List<LongtermPredictInputScaleDO> inputScaleDetail;
        // 需要拆分数据
        public final List<LongtermPredictOutputPurchaseDO> totalOriginData;

        // 存量的增量>0的数据明细， 废弃
        public List<LongtermPredictInputScaleIncreaseDTO> increaseDetail;

        // 当前在用的数据
        public List<LongtermPredictInputScaleWithIndustryDO> inputScaleWithIndustry;

        // 一共6份配置表
        List<CustomhouseTitleRate> customhouseTitleRate;
        List<BizRangeTypeRate> bizRangeTypeRate;
        List<InstanceFamilyRate> instanceFamilyRate;
        List<InstanceFamilyToDeviceTypeRate> instanceFamilyToDeviceTypeRate;
        List<InstanceFamilyToZoneNameRate> instanceFamilyToZoneNameRate;
        List<MonthRate> monthRate;

        // 再加一张大客户的表，页面填写的数据
        List<LongtermPredictInputBigCustomerPurchaseDO> allBigCustomerPurchase;

        public SplitDependDataDTO(LongtermPredictTaskDO taskDO, List<LongtermPredictInputArgsDO> allInputArgs,
                List<YearMonth> historyDatePoint,
                List<LongtermPredictInputScaleDO> inputScaleDetail,
                List<LongtermPredictOutputPurchaseDO> totalOriginData) {
            this.taskDO = taskDO;
            this.allInputArgs = allInputArgs;
            this.historyDatePoint = historyDatePoint;
            this.inputScaleDetail = inputScaleDetail;
            this.totalOriginData = totalOriginData;
        }
    }

    private void splitMonth(DeviceTypePurchaseDataSplit tree) {
        LongtermPredictTaskDO taskDO = tree.getSplitDependDataDTO().getTaskDO();
        // 目前只有一个来源，可以直接获取到开始的数据
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node : tree.getLeafLevel()) {
            LongtermPredictOutputPurchaseSplitDO needSplitData = node.getData();
            BigDecimal splitPurchaseCore = needSplitData.getPurchaseCore();
            splitPurchaseCore = splitPurchaseCore.subtract(tree.getRealMonthTotalPurchase());
            if (splitPurchaseCore.compareTo(BigDecimal.ZERO) < 0) {
                splitPurchaseCore = BigDecimal.ZERO;
            }
            int predictLen = 6 - tree.getRealMonthLen();
            splitPurchaseCore = splitPurchaseCore.divide(BigDecimal.valueOf(predictLen), 3, RoundingMode.HALF_UP);
            String msg = tree.getSplitOrder().getName() + ",平均拆分。";
            for (int i = 0; i < predictLen; i++) {
                LongtermPredictOutputPurchaseSplitDO oneSplit = new LongtermPredictOutputPurchaseSplitDO();
                BeanUtils.copyProperties(needSplitData, oneSplit);
                LocalDate startDate = needSplitData.getEndDate();
                YearMonth yearMonth = YearMonth.of(startDate.getYear(), startDate.getMonthValue());
                yearMonth = yearMonth.minusMonths(i);
                setByYearMonth(oneSplit, yearMonth);
                oneSplit.setPurchaseCore(splitPurchaseCore);
                if (taskDO.getPredictEnd().isBefore(oneSplit.getEndDate())) {
                    continue;
                }
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> treeNode =
                        DataSplitTreeNode.newNode(msg, oneSplit);
                tree.addNode(node, treeNode);
            }
        }
    }


    @SuppressWarnings("Convert2MethodRef")
    private void splitMonthV1(DeviceTypePurchaseDataSplit tree) {

        LongtermPredictTaskDO taskDO = tree.getSplitDependDataDTO().getTaskDO();
        // 目前只有一个来源，可以直接获取到开始的数据
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node : tree.getLeafLevel()) {
            LongtermPredictOutputPurchaseSplitDO needSplitData = node.getData();

            boolean needSubHistory = !taskDO.getPredictStart().withDayOfMonth(1)
                    .isBefore(needSplitData.getStartDate().withDayOfMonth(1));
            BigDecimal splitPurchaseCore = needSplitData.getPurchaseCore();
            if (needSubHistory) {// 减去已经发生了的值
                if (!taskDO.isDecisionCategory()) {
                    BigDecimal curRate = node.getSplitInfo().getRate(); // 依赖于国家的拆分数据
                    // 查询的时候，限定了时间范围，未来的时候，这里的值应该是 0
                    splitPurchaseCore = splitPurchaseCore.subtract(tree.getRealMonthTotalPurchase().multiply(curRate));
                } else {
                    List<PurchaseRealMonthDetail> realMonthDetail = tree.getRealMonthDetail();
                    BigDecimal realMonthTotalPurchase = EStream.of(realMonthDetail).filter((o) -> {
                                // 优先 regionName
                                if (Strings.isNotBlank(needSplitData.getRegionName())) {
                                    return Strings.equals(o.getRegionName(), needSplitData.getRegionName());
                                } else if (Strings.isNotBlank(needSplitData.getCountryName())) {
                                    return Strings.equals(o.getCountryName(), needSplitData.getCountryName());
                                } else {
                                    throw BizException.makeThrow("regionName和RegionName 都是空的");
                                }
                            }
                    ).sum((o) -> o.getRealMonthTotalPurchase());
                    splitPurchaseCore = splitPurchaseCore.subtract(realMonthTotalPurchase);
                }
                if (splitPurchaseCore.compareTo(BigDecimal.ZERO) < 0) {
                    splitPurchaseCore = BigDecimal.ZERO;
                }
            }

            int predictLen = 6;
            // 这里的bug原因： 方案200 的一条 input输出，拆分成了2条的输出，一条上半年，一条下半年， 下半年的结果也用了同一个input
            if (needSubHistory) {
                predictLen = 6 - tree.getRealMonthLen();
            }
//            splitPurchaseCore = splitPurchaseCore.divide(BigDecimal.valueOf(predictLen), 3, RoundingMode.HALF_UP);
            String msg = tree.getSplitOrder().getName() + ",按权重拆分，";
            List<YearMonth> totalSplitYearMonth = getSplitMonthByLen(needSplitData.getEndDate(), predictLen);
            List<MonthRate> monthConfigRate = tree.getSplitDependDataDTO().getMonthRate();

            Map<YearMonth, MonthRate> monthConfigRateMap = EStream.of(monthConfigRate)
                    .filter((o) -> Strings.equals(o.getCustomhouseTitle(), needSplitData.getCustomhouseTitle())
                            && totalSplitYearMonth.contains(o.getYm())).toMap((o) -> o.getYm(), (o) -> o);
            BigDecimal allConfigRateSum = EStream.of(monthConfigRateMap.values()).sum((o) -> o.getRate());
            // 如果需要拆分的月，每个月都配置了数据，才使用配置的权重
            boolean useConfig = monthConfigRateMap.size() == predictLen;
            if (!useConfig) {
                msg += "所在年月的月拆分比例异常;";
            }

            // 拆分逻辑
            for (YearMonth yearMonth : totalSplitYearMonth) {
                LongtermPredictOutputPurchaseSplitDO oneSplit = new LongtermPredictOutputPurchaseSplitDO();
                BeanUtils.copyProperties(needSplitData, oneSplit);
                setByYearMonth(oneSplit, yearMonth);
                BigDecimal realRate;
                if (useConfig) {
                    MonthRate config = monthConfigRateMap.get(yearMonth);
                    if (config == null) {
                        throw BizException.makeThrow("年月配置不合法，未找到配置: %s", yearMonth);
                    }
                    realRate = config.getRate().divide(allConfigRateSum, 8, RoundingMode.HALF_UP);
                } else {
                    // 配置不合法，直接平均分
                    realRate = BigDecimal.ONE.divide(BigDecimal.valueOf(predictLen), 8, RoundingMode.HALF_UP);
                }
                oneSplit.setPurchaseCore(realRate.multiply(splitPurchaseCore));

                // 最后面的几个月的数据，不要存到DB中，让数据看起来像18个月的
                if (taskDO.getPredictEnd().isBefore(oneSplit.getEndDate())) {
                    continue;
                }
                String tmp = yearMonth.getMonthValue() + "月权重：" + realRate.setScale(3, RoundingMode.HALF_UP);
                // 添加到下一层节点
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> treeNode =
                        DataSplitTreeNode.newNode(msg + tmp, oneSplit);
                tree.addNode(node, treeNode);
            }
        }
    }

    private static List<YearMonth> getSplitMonthByLen(LocalDate endDate, int predictLen) {
        List<YearMonth> totalSplitYearMonth = new ArrayList<>();
        for (int i = 0; i < predictLen; i++) {
            YearMonth yearMonth = YearMonth.of(endDate.getYear(), endDate.getMonthValue());
            yearMonth = yearMonth.minusMonths(i);
            totalSplitYearMonth.add(yearMonth);
        }
        return totalSplitYearMonth;
    }

    @SuppressWarnings("Convert2MethodRef")
    private void splitMonthV1(IndustryDeptPurchaseDataSplit tree) {
        LongtermPredictTaskDO taskDO = tree.getSplitDependDataDTO().getTaskDO();
        // 目前只有一个来源，可以直接获取到开始的数据
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> node : tree.getLeafLevel()) {
            LongtermPredictOutputPurchaseSplitIndustryDeptDO needSplitData = node.getData();
            BigDecimal splitPurchaseCore = needSplitData.getPurchaseCore();
            {// 减去已经发生了的值
                if (!taskDO.isDecisionCategory()) {
                    BigDecimal curRate = node.getSplitInfo().getRate(); // 依赖于国家的拆分数据
                    // 查询的时候，限定了时间范围，未来的时候，这里的值应该是 0
                    splitPurchaseCore = splitPurchaseCore.subtract(tree.getRealMonthTotalPurchase().multiply(curRate));
                } else {
                    List<PurchaseRealMonthDetail> realMonthDetail = tree.getRealMonthDetail();
                    BigDecimal realMonthTotalPurchase = EStream.of(realMonthDetail).filter((o) -> {
                                // 优先 regionName
                                if (Strings.isNotBlank(needSplitData.getRegionName())) {
                                    return Strings.equals(o.getRegionName(), needSplitData.getRegionName());
                                } else if (Strings.isNotBlank(needSplitData.getCountryName())) {
                                    return Strings.equals(o.getCountryName(), needSplitData.getCountryName());
                                } else {
                                    throw BizException.makeThrow("regionName和RegionName 都是空的");
                                }
                            }
                    ).sum((o) -> o.getRealMonthTotalPurchase());
                    splitPurchaseCore = splitPurchaseCore.subtract(realMonthTotalPurchase);
                }
                if (splitPurchaseCore.compareTo(BigDecimal.ZERO) < 0) {
                    splitPurchaseCore = BigDecimal.ZERO;
                }
            }
            if (splitPurchaseCore.compareTo(BigDecimal.ZERO) < 0) {
                splitPurchaseCore = BigDecimal.ZERO;
            }
            int predictLen = 6 - tree.getRealMonthLen();
//            splitPurchaseCore = splitPurchaseCore.divide(BigDecimal.valueOf(predictLen), 3, RoundingMode.HALF_UP);
            String msg = tree.getSplitOrder().getName() + ",按权重拆分，";
            List<YearMonth> totalSplitYearMonth = getSplitMonthByLen(needSplitData.getEndDate(), predictLen);
            List<MonthRate> monthConfigRate = tree.getSplitDependDataDTO().getMonthRate();
            Map<YearMonth, MonthRate> monthConfigRateMap = EStream.of(monthConfigRate)
                    .filter((o) -> Strings.equals(o.getCustomhouseTitle(), needSplitData.getCustomhouseTitle())
                            && totalSplitYearMonth.contains(o.getYm())).toMap((o) -> o.getYm(), (o) -> o);
            BigDecimal allConfigRateSum = EStream.of(monthConfigRateMap.values()).sum((o) -> o.getRate());
            // 如果需要拆分的月，每个月都配置了数据，才使用配置的权重
            boolean useConfig = monthConfigRateMap.size() == predictLen;
            if (!useConfig) {
                msg += "所在年月的月拆分比例异常;";
            }

            // 拆分逻辑
            for (YearMonth yearMonth : totalSplitYearMonth) {
                LongtermPredictOutputPurchaseSplitIndustryDeptDO oneSplit
                        = new LongtermPredictOutputPurchaseSplitIndustryDeptDO();
                BeanUtils.copyProperties(needSplitData, oneSplit);
                setByYearMonth(oneSplit, yearMonth);

                BigDecimal realRate;
                if (useConfig) {
                    MonthRate config = monthConfigRateMap.get(yearMonth);
                    if (config == null) {
                        throw BizException.makeThrow("年月配置不合法，未找到配置: %s", yearMonth);
                    }
                    realRate = config.getRate().divide(allConfigRateSum, 8, RoundingMode.HALF_UP);
                } else {
                    // 配置不合法，直接平均分
                    realRate = BigDecimal.ONE.divide(BigDecimal.valueOf(predictLen), 8, RoundingMode.HALF_UP);
                }
                oneSplit.setPurchaseCore(realRate.multiply(splitPurchaseCore));
                // 最后面的几个月的数据，不要存到DB中，让数据看起来像18个月的
                if (taskDO.getPredictEnd().isBefore(oneSplit.getEndDate())) {
                    continue;
                }
                String tmp = yearMonth.getMonthValue() + "月权重：" + realRate.setScale(3, RoundingMode.HALF_UP);
                // 添加到下一层节点
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> treeNode =
                        DataSplitTreeNode.newNode(msg + tmp, oneSplit);
                tree.addNode(node, treeNode);
            }
        }
    }


    private void splitMonth(IndustryDeptPurchaseDataSplit tree) {
        DataSplitOrder splitOrder = tree.getSplitOrder();
        LongtermPredictTaskDO taskDO = tree.getSplitDependDataDTO().getTaskDO();
        // 目前只有一个来源，可以直接获取到开始的数据
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> node : tree.getLeafLevel()) {
            LongtermPredictOutputPurchaseSplitIndustryDeptDO needSplitData = node.getData();
            BigDecimal splitPurchaseCore = needSplitData.getPurchaseCore();
            splitPurchaseCore = splitPurchaseCore.subtract(tree.getRealMonthTotalPurchase());
            if (splitPurchaseCore.compareTo(BigDecimal.ZERO) < 0) {
                splitPurchaseCore = BigDecimal.ZERO;
            }
            int predictLen = 6 - tree.getRealMonthLen();
            splitPurchaseCore = splitPurchaseCore.divide(BigDecimal.valueOf(predictLen), 3, RoundingMode.HALF_UP);
            String msg = splitOrder.getName() + ",平均拆分。";
            for (int i = 0; i < predictLen; i++) {
                LongtermPredictOutputPurchaseSplitIndustryDeptDO oneSplit = new LongtermPredictOutputPurchaseSplitIndustryDeptDO();
                splitOrder.setSplitCurKey(oneSplit, "df");
                BeanUtils.copyProperties(needSplitData, oneSplit);
                LocalDate startDate = needSplitData.getEndDate();
                YearMonth yearMonth = YearMonth.of(startDate.getYear(), startDate.getMonthValue());
                yearMonth = yearMonth.minusMonths(i);
                setByYearMonth(oneSplit, yearMonth);
                oneSplit.setPurchaseCore(splitPurchaseCore);
                if (taskDO.getPredictEnd().isBefore(oneSplit.getEndDate())) {
                    continue;
                }
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> treeNode =
                        DataSplitTreeNode.newNode(msg, oneSplit);
                tree.addNode(node, treeNode);
            }
        }
    }

    private void splitByOrder(DeviceTypePurchaseDataSplit tree) {
        DataSplitOrder splitOrder = tree.getSplitOrder();
        // 特殊拆分
        if (Strings.equals(splitOrder.getName(), DeviceTypePurchaseDataSplitOrder.CUSTOM_TITLE_HOUSE_SPLIT)) {
            splitCustomhouseTitleByConfig(tree); // decision 只关联境内外的数据，不拆分数据
            //v1 拆分逻辑，使用历史数据: splitByOrder0(tree);
        } else if (Strings.equals(splitOrder.getName(), DeviceTypePurchaseDataSplitOrder.MONTH_SPLIT)) {
            splitMonthV1(tree);
        } else if (Strings.equals(splitOrder.getName(), DeviceTypePurchaseDataSplitOrder.CUSTOMER_SPLIT)) {
            splitCustomerByConfig(tree);
        } else if (Strings.equals(splitOrder.getName(), DeviceTypePurchaseDataSplitOrder.INSTANCE_FAMILY_SPLIT)) {
            splitInstanceTypeFamilyByConfig(tree);
        } else if (Strings.equals(splitOrder.getName(), DeviceTypePurchaseDataSplitOrder.ZONE_NAME_SPLIT)) {
            splitZoneNameByConfig(tree);
        } else if (Strings.equals(splitOrder.getName(), DeviceTypePurchaseDataSplitOrder.DEVICE_SPLIT)) {
            splitDeviceV2(tree);
        }

    }


    private void splitCustomhouseTitleByConfig(DeviceTypePurchaseDataSplit tree) {

        // 2024-12-12 新增产品决策的拆分方案
        LongtermPredictTaskDO taskDO = tree.getSplitDependDataDTO().getTaskDO();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
        Map<String, String> countryNameInfoMap = EStream.of(regionNameInfoMap.values())
                .toMap(SoeRegionNameCountryDO::getCountryName, SoeRegionNameCountryDO::getCustomhouseTitle,
                        (a, b) -> a);

        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> oneLeaf : tree.getLeafLevel()) {
            LongtermPredictOutputPurchaseSplitDO needSplit = oneLeaf.getData();
            if (taskDO.isDecisionCategory()) { // 只关联境内的数据
                setCustomhouseTitle(needSplit, regionNameInfoMap, countryNameInfoMap);
                continue;
            }
            List<CustomhouseTitleRate> dbConfig = tree.getSplitDependDataDTO().getCustomhouseTitleRate();
            for (CustomhouseTitleRate config : dbConfig) {
                String msg = String.format("拆分%s, %s比例拆分", config.getCustomhouseTitle(), config.getRate());
                LongtermPredictOutputPurchaseSplitDO clone = JSON.clone(needSplit);
                clone.setCustomhouseTitle(config.getCustomhouseTitle());
                clone.appendNote(msg);
                BigDecimal newPurchaseCore = needSplit.getPurchaseCore().multiply(config.getRate());
                clone.setPurchaseCore(newPurchaseCore);
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node = DataSplitTreeNode.newNode(msg, clone);
                SplitInfo splitInfo = new SplitInfo();
                splitInfo.setRate(config.getRate());
                splitInfo.setKey(config.getCustomhouseTitle());
                node.setSplitInfo(splitInfo);
                tree.addNode(oneLeaf, node);
            }
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), needSplit.getPurchaseCore());
        }
    }

    private static void setCustomhouseTitle(RegionAccessor needSplit,
            Map<String, SoeRegionNameCountryDO> regionNameInfoMap,
            Map<String, String> countryNameInfoMap) {
        String countryName = needSplit.getCountryName();
        if (Strings.isNotBlank(countryName) && countryNameInfoMap.containsKey(countryName)) {
            String customhouseTitle = countryNameInfoMap.get(countryName);
            if (Strings.isNotBlank(customhouseTitle)) {
                needSplit.setCustomhouseTitle(customhouseTitle);
            }
        }
        String regionName = needSplit.getRegionName();
        if (Strings.isNotBlank(regionName) && regionNameInfoMap.containsKey(regionName)) {
            SoeRegionNameCountryDO config = regionNameInfoMap.get(regionName);
            if (config != null && Strings.isNotBlank(config.getCustomhouseTitle())) {
                needSplit.setCustomhouseTitle(config.getCustomhouseTitle());
                needSplit.setCountryName(config.getCountryName());
            }
        }
        if (Strings.isBlank(needSplit.getCustomhouseTitle())) {
            String format = "bas_soe_region_name_country 未找到境内外的数据，regionName: %s, countryName: %s";
            throw BizException.makeThrow(format, regionName, countryName);
        }
    }


    private void splitZoneNameByConfig(DeviceTypePurchaseDataSplit tree) {

        List<InstanceFamilyToZoneNameRate> dbConfig =
                tree.getSplitDependDataDTO().getInstanceFamilyToZoneNameRate();
        Map<SplitZoneNameKeyGetter.Key, List<InstanceFamilyToZoneNameRate>> groupByKey =
                ListUtils.groupBy(dbConfig, SplitZoneNameKeyGetter::splitZoneNamePrefixKey);
        Map<String, String> zoneName2RegionName = longtermPredictDictService.getZoneName2RegionName();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
        LongtermPredictTaskDO taskDO = tree.getSplitDependDataDTO().getTaskDO();

        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> oneLeaf : tree.getLeafLevel()) {
            if (dealNotSplitNode(tree, oneLeaf, "不拆分")) {
                continue;
            }
            LongtermPredictOutputPurchaseSplitDO needSplit = oneLeaf.getData();
            List<InstanceFamilyToZoneNameRate> configs = groupByKey.get(needSplit.splitZoneNamePrefixKey());
            configs = EStream.of(configs)
                    .filter((config) -> config.getRate() != null && config.getRate().compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.toList());

            // 如果是业务决定的这部分数据，需要过滤一下 region和country
            if (taskDO.isDecisionCategory()) {
                configs = EStream.of(configs).filter((config) -> {
                    String regionName = zoneName2RegionName.get(config.getZoneName());
                    if (Strings.isBlank(regionName)) {
                        throw BizException.makeThrow("可用区未找到城市的映射关系: %s", config.getZoneName());
                    }
                    if (Strings.isNotBlank(needSplit.getRegionName())) {
                        return Strings.equals(regionName, needSplit.getRegionName());
                    } else if (Strings.isNotBlank(needSplit.getCountryName())) {
                        SoeRegionNameCountryDO soeRegionNameCountryDO = regionNameInfoMap.get(regionName);
                        String countryName = soeRegionNameCountryDO.getCountryName();
                        if (Strings.isBlank(countryName)) {
                            throw BizException.makeThrow("regionName未找到国家的映射关系: %s", regionName);
                        }
                        return Strings.equals(countryName, needSplit.getCountryName());
                    } else {
                        throw BizException.makeThrow("region和country 都是空的");
                    }
                }).toList();
            }
            // 根据split去限制config
            SplitConfig splitConfig = taskDO.getSplitConfig();
            if (splitConfig != null) {
                Boolean regionLimitByInput = splitConfig.getRegionLimitByInput();
                if (regionLimitByInput != null && regionLimitByInput) {
                    // 获取历史的数据，然后限制范围
                    List<String> inputRegionNames = tree.getLastMonthRealPurchaseDetail().stream()
                            .map(PurchaseRealMonthDetail::getRegionName).distinct()
                            .collect(Collectors.toList());
                    configs = EStream.of(configs).filter((config) -> {
                        String regionName = zoneName2RegionName.get(config.getZoneName());
                        if (Strings.isBlank(regionName)) {
                            throw BizException.makeThrow("可用区未找到城市的映射关系: %s", config.getZoneName());
                        }
                        return inputRegionNames.contains(regionName);
                    }).toList();
                }
            }

            if (configs.isEmpty()) {
                String msg = String.format("未找到拆分可用区的配置:instanceFamily:%s,houseTitle:%s,regionName:%s",
                        needSplit.getInstanceFamily(), needSplit.getCustomhouseTitle(), needSplit.getRegionName());
                oneLeaf.setNeedSplit(false);
                if (dealNotSplitNode(tree, oneLeaf, msg)) {
                    continue;
                }
            }

            BigDecimal totalRate = NumberUtils.sum(configs, InstanceFamilyToZoneNameRate::getRate);

            for (InstanceFamilyToZoneNameRate config : configs) {
                String msg = String.format("拆分%s, %s比例拆分", config.getZoneName(), config.getRate());

                LongtermPredictOutputPurchaseSplitDO clone = JSON.clone(needSplit);
                clone.setZoneName(config.getZoneName());
                clone.appendNote(msg);
                if (config.getRate().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                BigDecimal newPurchaseCore = needSplit.getPurchaseCore().multiply(config.getRate())
                        .divide(totalRate, 6, RoundingMode.HALF_UP);
//                BigDecimal newPurchaseCore = needSplit.getPurchaseCore().multiply(config.getRate());
                clone.setPurchaseCore(newPurchaseCore);
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node = DataSplitTreeNode.newNode(msg, clone);
                tree.addNode(oneLeaf, node);
            }
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), needSplit.getPurchaseCore());
        }
        setCampusInfo(tree);
    }

    private void splitInstanceTypeFamilyByConfig(DeviceTypePurchaseDataSplit tree) {

        Map<String, String> zoneName2RegionName = longtermPredictDictService.getZoneName2RegionName();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();

        LongtermPredictTaskDO taskDO = tree.getSplitDependDataDTO().getTaskDO();
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> oneLeaf : tree.getLeafLevel()) {
            if (dealNotSplitNode(tree, oneLeaf, "")) {
                continue;
            }
            LongtermPredictOutputPurchaseSplitDO needSplit = oneLeaf.getData();

            List<InstanceFamilyRate> dbConfig = tree.getSplitDependDataDTO().getInstanceFamilyRate();
            if (taskDO.isDecisionCategory()) {
                // 这个是zoneName的拆分比例
                List<InstanceFamilyToZoneNameRate> instanceFamilyToZoneNameRate
                        = tree.getSplitDependDataDTO().getInstanceFamilyToZoneNameRate();
                List<String> instanceFamily = EStream.of(instanceFamilyToZoneNameRate).filter((config) -> {
                    if (config.getRate() == null || config.getRate().compareTo(BigDecimal.ZERO) <= 0) {
                        return false;
                    }
                    String regionName = zoneName2RegionName.get(config.getZoneName());
                    if (Strings.isBlank(regionName)) {
                        throw BizException.makeThrow("可用区未找到城市的映射关系: %s", config.getZoneName());
                    }
                    if (Strings.isNotBlank(needSplit.getRegionName())) {
                        return Strings.equals(regionName, needSplit.getRegionName());
                    } else if (Strings.isNotBlank(needSplit.getCountryName())) {
                        SoeRegionNameCountryDO soeRegionNameCountryDO = regionNameInfoMap.get(regionName);
                        String countryName = soeRegionNameCountryDO.getCountryName();
                        if (Strings.isBlank(countryName)) {
                            throw BizException.makeThrow("regionName未找到国家的映射关系: %s", regionName);
                        }
                        return Strings.equals(countryName, needSplit.getCountryName());
                    } else {
                        throw BizException.makeThrow("region和country 都是空的");
                    }
                }).map(InstanceFamilyToZoneNameRate::getInstanceFamily).toList();
                if (instanceFamily.isEmpty()) {
                    String msg = String.format(
                            "业务决策，配置了城市，但是未找到拆分可用区的配置:instanceFamily:%s,houseTitle:%s,regionName:%s",
                            needSplit.getInstanceFamily(), needSplit.getCustomhouseTitle(), needSplit.getRegionName());
                    oneLeaf.setNeedSplit(false);
                    dealNotSplitNode(tree, oneLeaf, msg);
                    continue;
                }
                dbConfig = EStream.of(dbConfig).filter((o) -> instanceFamily.contains(o.getInstanceFamily())).toList();
            }

            // 根据split去限制config
            SplitConfig splitConfig = taskDO.getSplitConfig();
            if (splitConfig != null) {
                Boolean regionLimitByInput = splitConfig.getRegionLimitByInput();
                if (regionLimitByInput != null && regionLimitByInput) {
                    // 获取历史的数据，然后限制范围
                    List<String> inputRegionNames = tree.getLastMonthRealPurchaseDetail().stream()
                            .map(PurchaseRealMonthDetail::getRegionName).distinct()
                            .collect(Collectors.toList());
                    // 这个是zoneName的拆分比例
                    List<InstanceFamilyToZoneNameRate> instanceFamilyToZoneNameRate
                            = tree.getSplitDependDataDTO().getInstanceFamilyToZoneNameRate();
                    List<String> instanceFamily = EStream.of(instanceFamilyToZoneNameRate).filter((config) -> {
                        String regionName = zoneName2RegionName.get(config.getZoneName());
                        if (Strings.isBlank(regionName)) {
                            throw BizException.makeThrow("可用区未找到城市的映射关系: %s", config.getZoneName());
                        }
                        return inputRegionNames.contains(regionName);
                    }).map(InstanceFamilyToZoneNameRate::getInstanceFamily).toList();
                    if (instanceFamily.isEmpty()) {
                        String msg = String.format(
                                "业务决策，配置了城市，但是未找到拆分可用区的配置:instanceFamily:%s,houseTitle:%s,regionName:%s",
                                needSplit.getInstanceFamily(), needSplit.getCustomhouseTitle(),
                                inputRegionNames);
                        oneLeaf.setNeedSplit(false);
                        dealNotSplitNode(tree, oneLeaf, msg);
                        continue;
                    }
                    dbConfig = EStream.of(dbConfig).filter((o) -> instanceFamily.contains(o.getInstanceFamily()))
                            .toList();
                }
            }
            if (dbConfig.isEmpty()) {
                throw BizException.makeThrow(
                        "可用区配置中未找到拆分实例大类的配置:instanceFamily:%s,houseTitle:%s,regionName:%s",
                        needSplit.getInstanceFamily(), needSplit.getCustomhouseTitle(), needSplit.getRegionName());
            }

            Map<SplitInstanceFamilyKeyGetter.Key, List<InstanceFamilyRate>> groupByKey =
                    ListUtils.groupBy(dbConfig, SplitInstanceFamilyKeyGetter::splitInstanceFamilyPrefixKey);
            List<InstanceFamilyRate> configs = groupByKey.get(needSplit.splitInstanceFamilyPrefixKey());

            BigDecimal totalRate = NumberUtils.sum(configs, InstanceFamilyRate::getRate);
            for (InstanceFamilyRate config : configs) {
                String msg = String.format("拆分%s, %s比例拆分", config.getInstanceFamily(), config.getRate());
                LongtermPredictOutputPurchaseSplitDO clone = JSON.clone(needSplit);
                clone.setInstanceFamily(config.getInstanceFamily());
                clone.appendNote(msg);
                BigDecimal newPurchaseCore = needSplit.getPurchaseCore()
                        .multiply(config.getRate()).divide(totalRate, 6, RoundingMode.HALF_UP);
                clone.setPurchaseCore(newPurchaseCore);
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node = DataSplitTreeNode.newNode(msg, clone);
                tree.addNode(oneLeaf, node);
            }
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), oneLeaf.getData().getPurchaseCore());
        }
    }

    private static boolean dealNotSplitNode(DeviceTypePurchaseDataSplit tree,
            DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> oneLeaf, String msg) {
        if (!oneLeaf.getNeedSplit()) {
            LongtermPredictOutputPurchaseSplitDO clone = JSON.clone(oneLeaf.getData());
            DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node = DataSplitTreeNode.newNode(msg, clone);
            node.setNeedSplit(false);
            tree.addNode(oneLeaf, node);
            return true;
        }
        return false;
    }

    private static boolean dealNotSplitNode(IndustryDeptPurchaseDataSplit tree,
            DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> oneLeaf) {
        if (!oneLeaf.getNeedSplit()) {
            LongtermPredictOutputPurchaseSplitIndustryDeptDO clone = JSON.clone(oneLeaf.getData());
            DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> node = DataSplitTreeNode.newNode("",
                    clone);
            node.setNeedSplit(false);
            tree.addNode(oneLeaf, node);
            return true;
        }
        return false;
    }

    private static void throwExceptionIfDiffAbsGTOne(BigDecimal a, BigDecimal b) {
        // 每一次拆分，确定一下当前的拆分结果是不是全部一致的
        if (b.subtract(a).abs().compareTo(BigDecimal.ONE) >= 0) {
            throw BizException.makeThrow("拆分数据异常");
        }
    }

    private void splitCustomerByConfig(DeviceTypePurchaseDataSplit tree) {

        Set<LongtermPredictInputBigCustomerPurchaseDO> consumedBigCustomer = new HashSet<>();
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> oneLeaf : tree.getLeafLevel()) {
            LongtermPredictOutputPurchaseSplitDO needSplit = oneLeaf.getData();
            // 大客户是到可用区的，目前中国内地只有一个数据，以此大客户挂在这里
            boolean isOutBoard = !needSplit.getCustomhouseTitle().equals("境内");
            if (isOutBoard) {
                LongtermPredictOutputPurchaseSplitDO clone = JSON.clone(needSplit);
                clone.setCustomerType("非境内TOP2客户");
                tree.addNode(oneLeaf, DataSplitTreeNode.newNode("", clone));
                continue;
            }

            List<LongtermPredictInputBigCustomerPurchaseDO> allBigCustomerPurchase =
                    tree.getSplitDependDataDTO().getAllBigCustomerPurchase();
            List<LongtermPredictInputBigCustomerPurchaseDO> splitBigCustomer = EStream.of(allBigCustomerPurchase)
                    .filter((o) -> o.getYearMonthStr().equals(needSplit.getYearMonthStr())).toList();

            Map<String, String> deviceTypeToInstanceTypeMap =
                    longtermPredictDictService.getDeviceTypeToInstanceTypeMap();
            Map<String, String> deviceTypeToInstanceFamilyMap
                    = longtermPredictDictService.getDeviceTypeToInstanceFamilyMap();
            Map<String, String> instanceTypeToInstanceFamily = cvmPlanService.getInstanceType2InstanceFamily();

            BigDecimal needPurchaseCore = needSplit.getPurchaseCore();
            for (LongtermPredictInputBigCustomerPurchaseDO oneBigCustomer : splitBigCustomer) {
                if (consumedBigCustomer.contains(oneBigCustomer)) {
                    throw BizException.makeThrow("大客户已经被拆分过，存在重复拆分");
                }
                consumedBigCustomer.add(oneBigCustomer);
                // 如果当月的预测小于大客户填的预测，报错
                BigDecimal curBigCustomerCore = BigDecimal.valueOf(oneBigCustomer.getPurchaseCore());
                if (needPurchaseCore.compareTo(curBigCustomerCore) < 0) {
                    String errorInfo = "[%s,%s]的中长期总预测为:%d,小于大客户的预测量，请检查大客户当月的总量";
                    throw BizException.makeThrow(errorInfo,
                            needSplit.getCustomhouseTitle(),
                            needSplit.getYearMonthStr(),
                            needSplit.getPurchaseCore().intValue());
                }
                LongtermPredictOutputPurchaseSplitDO one = new LongtermPredictOutputPurchaseSplitDO();
                BeanUtils.copyProperties(needSplit, one);
                one.setCustomerType(oneBigCustomer.getCustomerName());
                String deviceType = oneBigCustomer.getDeviceType();
                one.setDeviceType(deviceType);
                if (deviceType != null) {
                    String instanceType = deviceTypeToInstanceTypeMap.get(deviceType);
                    if (instanceType == null) {
                        throw BizException.makeThrow("异常物理机:%s, 查到不到实例类型", deviceType);
                    }
                    one.setInstanceFamily(instanceTypeToInstanceFamily.get(instanceType));
                    // 特别注意,上面科天的 instanceType 找不到会直接报错,因为要物理机的核心数,找不到直接报错
                    if (Strings.isBlank(one.getInstanceFamily())) {
                        String instanceFamily = deviceTypeToInstanceFamilyMap.get(deviceType);
                        one.setInstanceFamily(instanceFamily);
                    }
                }
                // 拆分可用区的时候会设置值
                one.setZoneName(oneBigCustomer.getZoneName());
                one.setPurchaseCore(curBigCustomerCore);
                needPurchaseCore = needPurchaseCore.subtract(curBigCustomerCore);

                String msg = "大客户预测值拆分";
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node = DataSplitTreeNode.newNode(msg, one);
                node.setNeedSplit(false); //大客户的节点下游不要拆分了
                tree.addNode(oneLeaf, node);
            }

            LongtermPredictOutputPurchaseSplitDO clone = JSON.clone(oneLeaf.getData());
            clone.setPurchaseCore(needPurchaseCore);
            clone.setCustomerType("非TOP2客户");
            DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node = DataSplitTreeNode.newNode("", clone);
            tree.addNode(oneLeaf, node);

            // 每一次拆分，确定一下当前的拆分结果是不是全部一致的
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), needSplit.getPurchaseCore());
        }
    }

    private void splitCustomerByConfig(IndustryDeptPurchaseDataSplit tree) {

        Set<LongtermPredictInputBigCustomerPurchaseDO> consumedBigCustomer = new HashSet<>();
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> oneLeaf : tree.getLeafLevel()) {
            LongtermPredictOutputPurchaseSplitIndustryDeptDO needSplit = oneLeaf.getData();
            // 大客户是到可用区的，目前中国内地只有一个数据，以此大客户挂在这里
            boolean isOutBoard = !needSplit.getCustomhouseTitle().equals("境内");
            boolean innerBiz = !needSplit.getBizRangeType().equals("外部业务");
            if (isOutBoard && innerBiz) {
                LongtermPredictOutputPurchaseSplitIndustryDeptDO clone = JSON.clone(needSplit);
                clone.setCustomerType("非境内TOP2客户");
                tree.addNode(oneLeaf, DataSplitTreeNode.newNode("", clone));
                continue;
            }

            List<LongtermPredictInputBigCustomerPurchaseDO> allBigCustomerPurchase =
                    tree.getSplitDependDataDTO().getAllBigCustomerPurchase();
            List<LongtermPredictInputBigCustomerPurchaseDO> splitBigCustomer = EStream.of(allBigCustomerPurchase)
                    .filter((o) -> o.getYearMonthStr().equals(needSplit.getYearMonthStr())).toList();

            BigDecimal needPurchaseCore = needSplit.getPurchaseCore();
            for (LongtermPredictInputBigCustomerPurchaseDO oneBigCustomer : splitBigCustomer) {
                if (consumedBigCustomer.contains(oneBigCustomer)) {
                    throw BizException.makeThrow("大客户已经被拆分过，存在重复拆分");
                }
                consumedBigCustomer.add(oneBigCustomer);
                // 如果当月的预测小于大客户填的预测，报错
                BigDecimal curBigCustomerCore = BigDecimal.valueOf(oneBigCustomer.getPurchaseCore());
                if (needPurchaseCore.compareTo(curBigCustomerCore) < 0) {
                    String errorInfo = "[%s,%s]的中长期总预测为:%d,小于大客户的预测量，请检查大客户当月的总量";
                    throw BizException.makeThrow(errorInfo,
                            needSplit.getCustomhouseTitle(),
                            needSplit.getYearMonthStr(),
                            needSplit.getPurchaseCore().intValue());
                }
                LPOPSplitIndustryDeptDO one = new LPOPSplitIndustryDeptDO();
                BeanUtils.copyProperties(needSplit, one);
                one.setCustomerType(oneBigCustomer.getCustomerName());
                one.setBizRangeType("外部客户");
                one.setIndustryDept("战略客户部");
                one.setPurchaseCore(curBigCustomerCore);
                needPurchaseCore = needPurchaseCore.subtract(curBigCustomerCore);

                String msg = "大客户预测值拆分";
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> node
                        = DataSplitTreeNode.newNode(msg, one);
                node.setNeedSplit(false); //大客户的节点下游不要拆分了
                tree.addNode(oneLeaf, node);
            }

            LongtermPredictOutputPurchaseSplitIndustryDeptDO clone = JSON.clone(oneLeaf.getData());
            clone.setPurchaseCore(needPurchaseCore);
            clone.setCustomerType("非TOP2客户");
            DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> node
                    = DataSplitTreeNode.newNode("", clone);
            tree.addNode(oneLeaf, node);

            // 每一次拆分，确定一下当前的拆分结果是不是全部一致的
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), needSplit.getPurchaseCore());
        }
    }

    // 第一个迭代版本
    private void splitDeviceV2(DeviceTypePurchaseDataSplit tree) {

        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> oneLeaf : tree.getLeafLevel()) {
            if (dealNotSplitNode(tree, oneLeaf, "大客户不拆分")) {
                continue;
            }
            LongtermPredictOutputPurchaseSplitDO needSplit = oneLeaf.getData();
            ErrorUtil.throwIfNull(needSplit.getYearMonth(), "年月要在物理机前拆分,发现null");
            ErrorUtil.throwIfNull(needSplit.getInstanceFamily(), "机型大类要在物理机前拆分,发现null");

            if (checkInstanceFamily(needSplit)) {
                continue;
            }
            List<InstanceFamilyToDeviceTypeRate> dbConfig =
                    tree.getSplitDependDataDTO().getInstanceFamilyToDeviceTypeRate();
            Map<Key, List<InstanceFamilyToDeviceTypeRate>> groupByKey = ListUtils.groupBy(
                    dbConfig, SplitDeviceTypeKeyGetter::splitDeviceTypePrefixKey);
            List<InstanceFamilyToDeviceTypeRate> configs
                    = groupByKey.get(needSplit.splitDeviceTypePrefixKey());

            for (InstanceFamilyToDeviceTypeRate config : configs) {
                List<YearMonthItem> yearMonthItemList = config.getYearMonthItemList();
                if (yearMonthItemList == null) {
                    yearMonthItemList = new ArrayList<>();
                }
                yearMonthItemList.sort(Comparator.comparing(YearMonthItem::getYm));
                BigDecimal rate = null;
                String rateMsg = "";
                for (int i = yearMonthItemList.size() - 1; i >= 0; i--) {
                    YearMonthItem ymItem = yearMonthItemList.get(i);
                    if (!ymItem.getYm().isAfter(needSplit.getYearMonth()) && ymItem.getRate() != null) {
                        rate = ymItem.getRate();
                        rateMsg = ymItem.getYm().toString() + "特定月配置比例";
                        break; // 找到第一个就break
                    }
                }
                if (rate == null) {
                    rate = config.getRate();
                    rateMsg = "默认的拆分物理机比例";
                }
                String msg = String.format("拆分%s, %s ,%s比例拆分", config.getDeviceType(), rateMsg, rate);
                LongtermPredictOutputPurchaseSplitDO clone = JSON.clone(needSplit);
                clone.setDeviceType(config.getDeviceType());
                clone.appendNote(msg);
                BigDecimal newPurchaseCore = needSplit.getPurchaseCore().multiply(rate);
                clone.setPurchaseCore(newPurchaseCore);
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node = DataSplitTreeNode.newNode(msg, clone);
                tree.addNode(oneLeaf, node);
            }
            // 每一次拆分，确定一下当前的拆分结果是不是全部一致的
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), needSplit.getPurchaseCore());
        }
    }


    private static boolean checkInstanceFamily(LongtermPredictOutputPurchaseSplitDO data) {
        throwIfInstanceFamilyError(data.getInstanceFamily(), null);
        if (data.getPurchaseCore().compareTo(BigDecimal.ONE) < 0) {
            return true;
        }
        return false;
    }

    private void splitDevice(DeviceTypePurchaseDataSplit tree) {

        Function<LongtermPredictInputInstance2deviceRateDO, String> key1 =
                (o) -> Strings.join("@", o.getCustomhouseTitle(), o.getInstanceFamily());
        Map<String, List<LongtermPredictInputInstance2deviceRateDO>> rateMap = tree.getInstance2DeviceRate().stream()
                .collect(Collectors.groupingBy(key1));
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> oneLeaf : tree.getLeafLevel()) {
            LongtermPredictOutputPurchaseSplitDO data = oneLeaf.getData();
            String key = Strings.join("@", data.getCustomhouseTitle(), data.getInstanceFamily());
            List<LongtermPredictInputInstance2deviceRateDO> rates = rateMap.get(key);
            if (rates == null || rates.isEmpty()) {
                String msg = "拆分物理机没有找到比例配置，key为：" + key;
                LongtermPredictOutputPurchaseSplitDO clone = JSON.clone(data);
                clone.appendNote(msg);
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node =
                        DataSplitTreeNode.newNode(msg, clone);
                clone.setDeviceType("");
                tree.addNode(oneLeaf, node);
                continue;
            }
            if (data.getPurchaseCore().compareTo(BigDecimal.ONE) < 0) {
                continue;
            }

            BigDecimal sum = NumberUtils.sum(rates, LongtermPredictInputInstance2deviceRateDO::getRate);
            boolean isOne = sum.subtract(BigDecimal.ONE).abs().compareTo(BigDecimal.valueOf(0.01)) < 0;

            BigDecimal totalNeedSplit = oneLeaf.getData().getPurchaseCore();
            for (LongtermPredictInputInstance2deviceRateDO rate : rates) {
                if (rate.getRate().compareTo(BigDecimal.valueOf(0.01)) < 0) {
                    continue;
                }
                String msg = "拆分物理机比例为：" +
                        rate.getRate().multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP)
                        + "%";
                if (!isOne) {
                    msg += "(rate 求和不是1， 有未拆分的量,或者拆分多了)";
                }
                LongtermPredictOutputPurchaseSplitDO clone = JSON.clone(data);
                clone.appendNote(msg);
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node =
                        DataSplitTreeNode.newNode(msg, clone);
                clone.setDeviceType(rate.getDeviceType());

                BigDecimal newPurchaseCore = rate.getRate().multiply(data.getPurchaseCore());
                clone.setPurchaseCore(newPurchaseCore);
                totalNeedSplit = totalNeedSplit.subtract(newPurchaseCore);
                tree.addNode(oneLeaf, node);
            }

            if (totalNeedSplit.compareTo(BigDecimal.ONE) > 0) {
                String msg = "比例不对，有一部分没拆分的";
                LongtermPredictOutputPurchaseSplitDO clone = JSON.clone(data);
                clone.appendNote(msg + key);
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node =
                        DataSplitTreeNode.newNode(msg, clone);
                clone.setDeviceType("");
                clone.setPurchaseCore(totalNeedSplit);
                tree.addNode(oneLeaf, node);
            }
        }
    }


    @Deprecated
    private void splitByOrder0(DeviceTypePurchaseDataSplit tree) {

        DataSplitOrder splitOrder = tree.getSplitOrder();

        List<LongtermPredictInputScaleIncreaseDTO> increaseDetail = tree.getSplitDependDataDTO().getIncreaseDetail();

        Map<String, Map<String, SplitInfo>> columnHistoryMap =
                // 先按之前已经拆分了的数据聚合，为了过滤数据
                ListUtils2.groupAndApply(increaseDetail, splitOrder::getSplitPrefixKey,
                        (prefixKey, val) -> {
                            // 按当前的group数据聚合
                            return ListUtils2.groupAndApply(val, splitOrder::getSplitCurKey,
                                    SplitServiceImpl::getSplitInfo);
                        });
        columnHistoryMap.values().stream().map((o) -> new ArrayList<>(o.values())).forEach(SplitServiceImpl::setRate);

        // 保存一下历史数据
        tree.historyList.add(columnHistoryMap);
        // 当前需要拆分的数据
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> oneLeaf : tree.getLeafLevel()) {
            Map<String, SplitInfo> splitHistoryMap
                    = columnHistoryMap.get(splitOrder.getSplitPrefixKey(oneLeaf.getData()));
            if (splitHistoryMap == null || splitHistoryMap.isEmpty()) {
                continue;
            }

            // 分为2部分，一部分有增量比例拆分， 一部分没有增量的为0
            // 这样会预估多没有增量的部分，整体向下的时候可能不适用
            BigDecimal splitValue = oneLeaf.getData().getPurchaseCore();
            // 没有了就不拆分了
            if (splitValue.compareTo(BigDecimal.ONE) < 0) {
                continue;
            }
            for (Entry<String, SplitInfo> oneHistory : splitHistoryMap.entrySet()) {
                SplitInfo splitInfo = oneHistory.getValue();
                if (splitInfo == null) {
                    throw new RuntimeException("splitInfo is null");
                }
                BigDecimal rate = splitInfo.getRate();
                BigDecimal childrenValue = splitValue.multiply(rate);
                LongtermPredictOutputPurchaseSplitDO oneSplit = JSON.clone(oneLeaf.getData());
                oneSplit.setPurchaseCore(childrenValue);
                splitOrder.setSplitCurKey(oneSplit, splitInfo.getKey());
                String msg = "%s, rate: %f;";
                msg = String.format(msg, splitOrder.getSplitCurKey(oneSplit), rate.setScale(5, RoundingMode.HALF_UP));
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> node =
                        DataSplitTreeNode.newNode(msg, oneSplit.copyOne());
                node.setSplitInfo(splitInfo);
                tree.addNode(oneLeaf, node);
            }
            // 每一次拆分，确定一下当前的拆分结果是不是全部一致的
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), splitValue);
        }

        if (Strings.equals(splitOrder.getName(), DeviceTypePurchaseDataSplitOrder.ZONE_NAME_SPLIT)) {
            setCampusInfo(tree);
        }
    }

    private void setCampusInfo(DeviceTypePurchaseDataSplit tree) {
        Long taskId = tree.getSplitDependDataDTO().getTaskDO().getId();
        Map<String, String> zone2CampusInfoMap = longtermPredictDictService.getZoneNameToCampus(taskId);
        Map<String, String> zoneName2RegionName = longtermPredictDictService.getZoneName2RegionName();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = longtermPredictDictService.getRegionNameInfoMap();
        tree.getLeafLevel().forEach((o) -> {
            LongtermPredictOutputPurchaseSplitDO data = o.getData();
            String zoneName = o.getData().getZoneName();
            String campus = zone2CampusInfoMap.get(zoneName);
            String regionName = zoneName2RegionName.get(zoneName);
            // 没有指定region才覆盖
            if (Strings.isNotBlank(regionName) && Strings.isBlank(data.getRegionName())) {
                data.setRegionName(regionName);
            }
            SoeRegionNameCountryDO soeRegionNameCountryDO = regionNameInfoMap.get(regionName);
            if (soeRegionNameCountryDO != null) {
                data.setCountryName(soeRegionNameCountryDO.getCountryName());
                data.setCustomhouseTitle(soeRegionNameCountryDO.getCustomhouseTitle());
            }
            data.setCampus(campus);
            if (Strings.isBlank(campus)) {
                data.appendNote(String.format("可用区[%s]没有找到campus", zoneName));
            }
        });
    }

    private void splitByOrder(IndustryDeptPurchaseDataSplit tree) {

        List<LongtermPredictInputScaleWithIndustryDO> inputScaleDetail =
                tree.getSplitDependDataDTO().getInputScaleWithIndustry();
        DataSplitOrder splitOrder = tree.getSplitOrder();
        List<YearMonth> halfYearDatePoint = tree.getSplitDependDataDTO().getHistoryDatePoint();

        Map<String, Map<String, IndustryScaleSplitInfo>> columnHistoryMap =
                // 先按之前已经拆分了的数据聚合，为了过滤数据
                ListUtils2.groupAndApply(inputScaleDetail, splitOrder::getSplitPrefixKey,
                        (prefixKey, val) -> {
                            // 按当前的group数据聚合
                            return ListUtils2.groupAndApply(val, splitOrder::getSplitCurKey,
                                    (curSplitKey, list) -> getIndustrySplitInfo(curSplitKey, list, halfYearDatePoint));
                        });
        columnHistoryMap.values().stream()
                .map((o) -> o.values().stream().map((oo) -> (SplitInfo) oo).collect(Collectors.toList()))
                .forEach(SplitServiceImpl::setRate);

        // 保存一下历史数据
        tree.historyList.add(columnHistoryMap);
        // 当前需要拆分的数据
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> oneLeaf : tree.getLeafLevel()) {
            if (dealNotSplitNode(tree, oneLeaf)) {
                continue;
            }
            Map<String, IndustryScaleSplitInfo> splitHistoryMap
                    = columnHistoryMap.get(splitOrder.getSplitPrefixKey(oneLeaf.getData()));
            if (splitHistoryMap == null || splitHistoryMap.isEmpty()) {
                // throw new RuntimeException("splitInfo is null");
                // TODO 先绕过
                continue;
            }

            // 分为2部分，一部分有增量比例拆分， 一部分没有增量的为0
            // 这样会预估多没有增量的部分，整体向下的时候可能不适用
            BigDecimal splitValue = oneLeaf.getData().getPurchaseCore();
            for (Entry<String, IndustryScaleSplitInfo> oneHistory : splitHistoryMap.entrySet()) {
                IndustryScaleSplitInfo splitInfo = oneHistory.getValue();
                if (splitInfo == null) {
                    // throw new RuntimeException("splitInfo is null");
                    // TODO 先绕过
                    continue;
                }
                BigDecimal rate = splitInfo.getRate();
                BigDecimal childrenValue = splitValue.multiply(rate);
                LongtermPredictOutputPurchaseSplitIndustryDeptDO oneSplit = new LongtermPredictOutputPurchaseSplitIndustryDeptDO();
                BeanUtils.copyProperties(oneLeaf.getData(), oneSplit);
                oneSplit.setPurchaseCore(childrenValue);
                splitOrder.setSplitCurKey(oneSplit, splitInfo.getKey());
                String msg = "%s, rate: %f;";
                msg = String.format(msg, splitOrder.getSplitCurKey(oneSplit), rate);
                DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> node =
                        DataSplitTreeNode.newNode(msg, oneSplit);
                node.setSplitInfo(splitInfo);
                tree.addNode(oneLeaf, node);
            }
            // 每一次拆分，确定一下当前的拆分结果是不是全部一致的
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), splitValue);
        }


    }

    private static IndustryScaleSplitInfo getIndustrySplitInfo(String curSplitKey,
            List<LongtermPredictInputScaleWithIndustryDO> list, List<YearMonth> halfYearDatePoint) {
        IndustryScaleSplitInfo splitInfo = new IndustryScaleSplitInfo();
        splitInfo.setKey(curSplitKey);
        splitInfo.setHistoryData(list);
        Map<String, BigDecimal> dateSumMap = ListUtils2.groupAndApply(list,
                LongtermPredictInputScaleWithIndustryDO::getYearMonthStr,
                (date, dateList) -> NumberUtils.sum(dateList,
                        LongtermPredictInputScaleWithIndustryDO::getCurCore));
        BigDecimal diff = getDiff(halfYearDatePoint, dateSumMap);
        splitInfo.setDiffCoreNum(diff);
        splitInfo.setIsIncrease(diff.compareTo(BigDecimal.ZERO) > 0);
        if (!list.isEmpty()) {
            // todo list sort?
            splitInfo.setLastOneMonthCoreNum(list.get(list.size() - 1).getCurCore());
        }
        return splitInfo;
    }

    private static ScaleIncreaseSplitInfo getSplitInfo(
            String curSplitKey, List<LongtermPredictInputScaleIncreaseDTO> increaseDetail) {
        ScaleIncreaseSplitInfo splitInfo = new ScaleIncreaseSplitInfo();
        splitInfo.setKey(curSplitKey);
        splitInfo.setIncreaseDetail(increaseDetail);
        BigDecimal diff = NumberUtils.sum(increaseDetail, LongtermPredictInputScaleIncreaseDTO::getDiffCore);
        splitInfo.setDiffCoreNum(diff);
        splitInfo.setIsIncrease(diff.compareTo(BigDecimal.ZERO) > 0);
        return splitInfo;
    }

    private static ScaleSplitInfo getSplitInfo(String curSplitKey,
            List<LongtermPredictInputScaleDO> list, List<YearMonth> halfYearDatePoint) {
        ScaleSplitInfo splitInfo = new ScaleSplitInfo();
        splitInfo.setKey(curSplitKey);
        splitInfo.setHistoryData(list);
        Map<String, BigDecimal> dateSumMap = ListUtils2.groupAndApply(list,
                LongtermPredictInputScaleDO::getYearMonthStr,
                (date, dateList) -> NumberUtils.sum(dateList, LongtermPredictInputScaleDO::getCurCore));
        BigDecimal diff = getDiff(halfYearDatePoint, dateSumMap);
        splitInfo.setDiffCoreNum(diff);
        splitInfo.setIsIncrease(diff.compareTo(BigDecimal.ZERO) > 0);
        BigDecimal lastMonthCurCore = getLastMonthCurCore(halfYearDatePoint, dateSumMap);
        splitInfo.setLastOneMonthCoreNum(lastMonthCurCore);
        splitInfo.setFuture6MonthCoreNum(diff.add(lastMonthCurCore));
        return splitInfo;
    }

    private static BigDecimal getLastMonthCurCore(List<YearMonth> halfYearDatePoint,
            Map<String, BigDecimal> dateSumMap) {
        BigDecimal monthSub1 = dateSumMap.getOrDefault(halfYearDatePoint.get(0).toString(), BigDecimal.ZERO);
        if (monthSub1.compareTo(BigDecimal.ZERO) > 0) {
            return monthSub1;
        }
        return dateSumMap.getOrDefault(halfYearDatePoint.get(1).toString(), BigDecimal.ZERO);
    }

    private static BigDecimal getDiff(List<YearMonth> halfYearDatePoint, Map<String, BigDecimal> dateSumMap) {
        BigDecimal monthSub1 = dateSumMap.getOrDefault(halfYearDatePoint.get(0).toString(), BigDecimal.ZERO);
        BigDecimal monthSub6 = dateSumMap.getOrDefault(halfYearDatePoint.get(1).toString(), BigDecimal.ZERO);
        return monthSub1.subtract(monthSub6);
    }

    private static void setByYearMonth(LongtermPredictOutputPurchaseSplitIndustryDeptDO oneSplit, YearMonth yearMonth) {
        oneSplit.setStartDate(yearMonth.atDay(1));
        oneSplit.setEndDate(yearMonth.atEndOfMonth());
        int month = yearMonth.getMonthValue();
        int quarter = (month - 1) / 3 + 1;
        int halfYear = (month - 1) / 6 + 1;
        oneSplit.setQuarter(quarter);
        oneSplit.setHalfYear(halfYear);
        oneSplit.setYearMonthStr(yearMonth.toString());
        oneSplit.setYear(yearMonth.getYear());
    }

    private static void setByYearMonth(LongtermPredictOutputPurchaseSplitDO oneSplit, YearMonth yearMonth) {
        oneSplit.setStartDate(yearMonth.atDay(1));
        oneSplit.setEndDate(yearMonth.atEndOfMonth());
        int month = yearMonth.getMonthValue();
        int quarter = (month - 1) / 3 + 1;
        int halfYear = (month - 1) / 6 + 1;
        oneSplit.setQuarter(quarter);
        oneSplit.setHalfYear(halfYear);
        oneSplit.setYearMonthStr(yearMonth.toString());
        oneSplit.setYear(yearMonth.getYear());
    }


    private static final Supplier<String> replaceSplitConfig =
            DynamicProperty.create("replace_split_config", "");

    @Synchronized(keyScript = "args[0].taskId", waitLockMillisecond = 100,
            customExceptionMessage = "当前任务正在拆分，请等待上一个拆分完成后，再操作")
    @Transactional(value = "cdlabTransactionManager")
    @Override
    public void splitWithOutRet(CreateSplitVersionReq req) {
        LongtermPredictTaskDO taskDO = cdLabDbHelper.getOne(LongtermPredictTaskDO.class, "where id=?", req.getTaskId());
        if (taskDO == null) {
            throw new RuntimeException("task is not exist");
        }
        try {
            long start = System.currentTimeMillis();

            // 这里把默认的拆分比例也带上
            ConfigSplitRateResp config = queryConfigSplitRate(req.getTaskId());
            ConfigSplitRateDiffReq tempData = getConfigSplitRateDiffReq(config);
            req.setInstanceFamilyRate(tempData.getInstanceFamilyRate());
            req.setInstanceFamilyToDeviceTypeRate(tempData.getInstanceFamilyToDeviceTypeRate());
            req.setInstanceFamilyToZoneNameRate(tempData.getInstanceFamilyToZoneNameRate());
            req.setCustomhouseTitleRate(tempData.getCustomhouseTitleRate());
            req.setBizRangeTypeRate(tempData.getBizRangeTypeRate());
            req.setMonthRate(tempData.getMonthRate());

            Map<String, String> categoryMap = getCategoryConfigMap();

            String categoryName = taskDO.getCategoryName();
            if (categoryMap.containsKey(categoryName)) {
                categoryName = categoryMap.get(categoryName);
            }

            String historySplitSql = "select split_arg\n"
                    + "from cloud_demand_lab.longterm_predict_output_split_version  a\n"
                    + "         left join cloud_demand_lab.longterm_predict_task b\n"
                    + "                   on a.task_id = b.id\n"
                    + "where b.category_name = ? and b.is_enable in (1,2) and b.deleted=0 and a.deleted=0\n"
                    + "order by b.predict_start desc , a.id desc\n"
                    + "limit 1";
            String splitArg = cdLabDbHelper.getRawOne(String.class, historySplitSql, categoryName);
            if (Strings.isNotBlank(splitArg)) {
                CreateSplitVersionReq historyReq = JSON.parse(splitArg, CreateSplitVersionReq.class);
                String historyInfo = "taskId: " + historyReq.getTaskId() + ", name: " + historyReq.getName();
                historyReq.setTaskId(req.getTaskId());
                historyReq.setNote("使用上一次的拆分参数拆分," + historyInfo);
                req = historyReq;
            }

            getOriginTrees(req);
            if (!LongtermPredictTaskStatusEnum.SUCCESS.getCode()
                                .equals(taskDO.getTaskStatus())) { // 如果已经成功，就不再改状态了
                taskDO.setTaskStatus(LongtermPredictTaskStatusEnum.SUCCESS.getCode());
                taskDO.setTaskEndTime(LocalDateTime.now());
                taskDO.setSplitCostMs(System.currentTimeMillis() - start);
                cdLabDbHelper.update(taskDO);
            }
        } catch (Exception e) {
            log.error("split finally error, taskId:{}", req.getTaskId(), e);
            if (taskDO != null && !LongtermPredictTaskStatusEnum.SUCCESS.getCode()
                    .equals(taskDO.getTaskStatus())) { // 如果已经成功，就不再改状态了
                updateTaskStatusThreadPool.submit(() -> {
                    taskDO.setTaskStatus(LongtermPredictTaskStatusEnum.SPLIT_FAIL.getCode());
                    taskDO.setErrMsg(e.getMessage());
                    taskDO.setTaskEndTime(LocalDateTime.now());
                    cdLabDbHelper.update(taskDO);
                });
            }
            throw new RuntimeException(e); // 触发回滚事务
        }
    }

    private static Map<String, String> getCategoryConfigMap() {
        String config = replaceSplitConfig.get();
        Map<String, String> categoryMap = new HashMap<>();
        if (Strings.isNotBlank(config)) {
            String[] split = config.split(";;;");
            for (String kv : split) {
                if (Strings.isNotBlank(kv)) {
                    String[] kvParse = kv.split(":::");
                    if (kvParse.length == 2) {
                        categoryMap.put(kvParse[0], kvParse[1]);
                    }
                }
            }
        }
        return categoryMap;
    }

    @Synchronized(keyScript = "args[0].taskId", waitLockMillisecond = 100,
            customExceptionMessage = "当前任务正在拆分，请等待上一个拆分完成后，再操作")
    @Override
    @Transactional(value = "cdlabTransactionManager")
    public CreateSplitVersionResp splitData(CreateSplitVersionReq req) {
        LongtermPredictTaskDO task = self.getTask(req.getTaskId());
        if (task == null) {
            throw new RuntimeException("task is not exist");
        }
        OriginTrees result = getOriginTrees(req);
        // filter
        int predictYear = task.getPredictStart().getYear();
        result.deviceTypeSplitTrees = result.deviceTypeSplitTrees.stream()
                .filter((o) -> o.getCurInputArgs().getStartDate().getYear() <= predictYear + 1)
                .collect(Collectors.toList());
        result.scaleSplitTrees = result.scaleSplitTrees.stream()
                .filter((o) -> o.getCurInputArgs().getStartDate().getYear() <= predictYear + 1)
                .collect(Collectors.toList());
        result.industrySplitTrees = result.industrySplitTrees.stream()
                .filter((o) -> o.getCurInputArgs().getStartDate().getYear() <= predictYear + 1)
                .collect(Collectors.toList());
        // construct
        CreateSplitVersionResp resp = new CreateSplitVersionResp();
//        constructScaleWebRetTree(result, resp);
//        constructIndustryDeptWebRetTree(result, resp);
//        constructDeviceTypeWebRetTree(result, resp);
        resp.setSplitVersionId(result.splitVersionId);

        return resp;
    }

    private void constructDeviceTypeWebRetTree(OriginTrees result, CreateSplitVersionResp resp) {

        List<DeviceTypePurchaseDataSplit> deviceTypeSplitTrees = result.deviceTypeSplitTrees;
        // 构造返回的data
        ListUtils2.groupAndApply(deviceTypeSplitTrees, (o) -> {
            String strategyType = o.getCurInputArgs().getStrategyType();
            return StrategyTypeEnum.getByCode(strategyType);
        }, (key, list) -> {
            Table3 table3 = new Table3();
            List<DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO>> level3 = list.stream()
                    .flatMap((o) -> o.getLevels().get(3).stream()).collect(Collectors.toList());

            DateColumList dateColumList = getDateColumList(list);
            table3.setDateKey(dateColumList);
            table3.setTree(dfs3(level3, 0, new AtomicLong(0), dateColumList));
            table3.setStrategyType(key.getCode());
            table3.setStrategyTypeName(key.getName());
            resp.getTable3().add(table3);
            return table3;
        });
    }

    private static DateColumList getDateColumList(List<DeviceTypePurchaseDataSplit> list) {
        List<LongtermPredictOutputPurchaseSplitDO> collect = EStream.of(list).map(DataSplitTree::getAfterSplitData)
                .flatMap(Collection::stream).toList();
        DateColumList dateColumList = new DateColumList();
        EStream.of(collect).map(LongtermPredictOutputPurchaseSplitDO::getQuarterName).distinct().sorted()
                .consumeAsList(dateColumList::setQuarterDate);
        EStream.of(collect).map(LongtermPredictOutputPurchaseSplitDO::getHalfYearName).distinct().sorted()
                .consumeAsList(dateColumList::setHalfYearDate);
        EStream.of(collect).map(LongtermPredictOutputPurchaseSplitDO::getYearMonthStr).distinct().sorted()
                .consumeAsList(dateColumList::setMonthDate);
        return dateColumList;
    }

    private static DateColumList getDateColumList2(List<IndustryDeptPurchaseDataSplit> list) {
        List<LongtermPredictOutputPurchaseSplitIndustryDeptDO> collect = EStream.of(list)
                .map(DataSplitTree::getAfterSplitData).flatMap(Collection::stream).toList();
        DateColumList dateColumList = new DateColumList();
        EStream.of(collect).map(LongtermPredictOutputPurchaseSplitIndustryDeptDO::getQuarterName).distinct().sorted()
                .consumeAsList(dateColumList::setQuarterDate);
        EStream.of(collect).map(LongtermPredictOutputPurchaseSplitIndustryDeptDO::getHalfYearName).distinct().sorted()
                .consumeAsList(dateColumList::setHalfYearDate);
        EStream.of(collect).map(LongtermPredictOutputPurchaseSplitIndustryDeptDO::getYearMonthStr).distinct().sorted()
                .consumeAsList(dateColumList::setMonthDate);
        return dateColumList;
    }

    private static DateColumList getDateColumList1(List<ScaleDataSplit> list) {
        List<LongtermPredictOutputScaleSplitDO> collect = EStream.of(list).map(DataSplitTree::getAfterSplitData)
                .flatMap(Collection::stream).toList();
        DateColumList dateColumList = new DateColumList();
        EStream.of(collect).map(LongtermPredictOutputScaleSplitDO::getQuarterName).distinct().sorted()
                .consumeAsList(dateColumList::setQuarterDate);
        EStream.of(collect).map(LongtermPredictOutputScaleSplitDO::getHalfYearName).distinct().sorted()
                .consumeAsList(dateColumList::setHalfYearDate);
        EStream.of(collect).map(LongtermPredictOutputScaleSplitDO::getYearMonthStr).distinct().sorted()
                .consumeAsList(dateColumList::setMonthDate);
        return dateColumList;
    }


    private void constructIndustryDeptWebRetTree(OriginTrees result, CreateSplitVersionResp resp) {
        List<IndustryDeptPurchaseDataSplit> industrySplitTrees = result.industrySplitTrees;
        // 构造返回的data
        ListUtils2.groupAndApply(industrySplitTrees, (o) -> {
            String strategyType = o.getCurInputArgs().getStrategyType();
            return StrategyTypeEnum.getByCode(strategyType);
        }, (key, list) -> {
            Table2 table2 = new Table2();
            List<DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO>> level3 = list.stream()
                    .flatMap((o) -> o.getLevels().get(2).stream()).collect(Collectors.toList());
            DateColumList dateColumList = getDateColumList2(list);
            table2.setDateColumList(dateColumList);
            table2.setTree(dfs2(level3, 0, new AtomicLong(0), dateColumList));
            table2.setStrategyType(key.getCode());
            table2.setStrategyTypeName(key.getName());
            resp.getTable2().add(table2);
            return table2;
        });

    }

    private void constructScaleWebRetTree(OriginTrees result, CreateSplitVersionResp resp) {
        List<ScaleDataSplit> scaleSplitTrees = result.scaleSplitTrees;
        // 构造返回的data
        ListUtils2.groupAndApply(scaleSplitTrees, (o) -> {
            String strategyType = o.getCurInputArgs().getStrategyType();
            return StrategyTypeEnum.getByCode(strategyType);
        }, (key, list) -> {
            Table1 table1 = new Table1();
            List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> level3 = list.stream()
                    .flatMap((o) -> o.getLevels().get(2).stream()).collect(Collectors.toList());
            DateColumList dateColumList = getDateColumList1(list);
            table1.setDateColumList(dateColumList);
            table1.setTree(dfs(level3, 0, new AtomicLong(), dateColumList));
            table1.setStrategyType(key.getCode());
            table1.setStrategyTypeName(key.getName());
            resp.getTable1().add(table1);
            return table1;
        });
    }


    List<Tree1> dfs(List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> data, int level, AtomicLong incrId,
            DateColumList dateColumList) {
        // 构造Tree
        List<Tree1> ret = new ArrayList<>();
        ListUtils2.groupAndApply(data,
                (o) -> Tree1.from(o.getData()),
                (tree, list) -> {
                    tree.setId(incrId.getAndIncrement());
                    ret.add(tree);
                    setNumInfo(tree, list, dateColumList);
                    List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> childNode = list.stream()
                            .flatMap((o) -> o.getChildren().stream()).collect(Collectors.toList());
                    if (childNode.isEmpty()) {
                        return tree;
                    }
                    tree.setChild(dfs(childNode, 1 + level, incrId, dateColumList));
                    return tree;
                }
        );
        setLevel0Name(level, ret);
        return ret;
    }

    List<Tree2> dfs2(List<DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO>> data,
            int level, AtomicLong incrId, DateColumList dateColumList) {
        // 构造Tree
        List<Tree2> ret = new ArrayList<>();
        ListUtils2.groupAndApply(data,
                (o) -> Tree2.from(o.getData()),
                (tree, list) -> {
                    tree.setId(incrId.getAndIncrement());
                    ret.add(tree);
                    setNumInfo2(tree, list, dateColumList);
                    List<DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO>> childNode = list.stream()
                            .flatMap((o) -> o.getChildren().stream()).collect(Collectors.toList());
                    if (childNode.isEmpty()) {
                        return tree;
                    }
                    tree.setChild(dfs2(childNode, 1 + level, incrId, dateColumList));
                    return tree;
                }
        );
        setLevel0Name2(level, ret);
        return ret;
    }

    private List<Tree3> dfs3(List<DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO>> data,
            int level, AtomicLong incrId, DateColumList dateColumList) {
        // 构造Tree
        List<Tree3> ret = new ArrayList<>();
        ListUtils2.groupAndApply(data,
                (o) -> Tree3.from(o.getData()),
                (tree, list) -> {
                    ret.add(tree);
                    tree.setId(incrId.getAndIncrement());
                    setNumInfo3(tree, list, dateColumList);
                    Map<String, Integer> deviceTypeToCoreNumMap = longtermPredictDictService.getDeviceTypeToCoreNumMap();
                    tree.setDeviceCoreNum(deviceTypeToCoreNumMap.get(tree.getDeviceType()));
                    List<DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO>> childNode = list.stream()
                            .flatMap((o) -> o.getChildren().stream()).collect(Collectors.toList());
                    if (childNode.isEmpty()) {
                        return tree;
                    }
                    tree.setChild(dfs3(childNode, 1 + level, incrId, dateColumList));
                    return tree;
                }
        );
        setLevel0Name3(level, ret);
        return ret;

    }

    private static void setLevel0Name(int level, List<Tree1> ret) {
        if (level == 0) { // 第一层设置为全量
            for (Tree1 tree1 : ret) {
                tree1.setRegionInfo("全量");
                tree1.setCustomhouseTitle("全量");
                tree1.setInstanceFamily("全量");
                tree1.setBizRangeType("全量");
                tree1.setRegionName("全量");
            }
        }
    }

    private static void setLevel0Name2(int level, List<Tree2> ret) {
        if (level == 0) { // 第一层设置为全量
            for (Tree2 tree2 : ret) {
                tree2.setCustomhouseTitle("全量");
                tree2.setBizRangeType("全量");
                tree2.setIndustryDept("全量");
            }
        }
    }

    private static void setLevel0Name3(int level, List<Tree3> ret) {
        if (level == 0) { // 第一层设置为全量
            for (Tree3 tree3 : ret) {
                tree3.setCustomhouseTitle("全量");
                tree3.setRegionInfo("全量");
                tree3.setZoneName("全量");
                tree3.setCampus("全量");
                tree3.setInstanceFamily("全量");
                tree3.setDeviceType("全量");
            }
        }
    }


    private static void setNumInfo(Tree1 tree, List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> list,
            DateColumList dateColumList) {
        list.sort(Comparator.comparing((o) -> o.getData().getYearMonthStr()));
        List<String> dateJoin = new ArrayList<>();
        List<BigDecimal> curCore = new ArrayList<>();
        list.stream().map(DataSplitTreeNode::getData).forEach((o) -> {
            dateJoin.add(Strings.join("@", o.getYearMonthStr(), o.getQuarter(), o.getHalfYear()));
            curCore.add(o.getCurCore().setScale(1, RoundingMode.HALF_UP));
        });
        tree.setDateJoin(dateJoin);
        tree.setCurCore(curCore);

        /*
        Map<String, BigDecimal> quarterMap = EStream.of(list).groupBy((o) -> o.getData().getQuarterName())
                .toMap(Entry::getKey, (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getData().getCurCore()));
        EStream.of(dateColumList.getQuarterDate()).map((o) -> getFromMap(quarterMap, o))
                .consumeAsList(tree::setQuarterPurchaseCore);
        Map<String, BigDecimal> halfYearMap = EStream.of(list).groupBy((o) -> o.getData().getHalfYearName())
                .toMap(Entry::getKey, (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getData().getCurCore()));
        EStream.of(dateColumList.getHalfYearDate()).map((o) -> getFromMap(halfYearMap, o))
                .consumeAsList(tree::setHalfYearPurchaseCore);
        Map<String, BigDecimal> monthMap = EStream.of(list).groupBy((o) -> o.getData().getYearMonthStr())
                .toMap(Entry::getKey, (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getData().getCurCore()));
        EStream.of(dateColumList.getMonthDate()).map((o) -> getFromMap(monthMap, o))
                .consumeAsList(tree::setMonthPurchaseCore);
        */

    }

    private static void setNumInfo2(Tree2 tree,
            List<DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO>> list,
            DateColumList dateColumList) {
        list.sort(Comparator.comparing((o) -> o.getData().getYearMonthStr()));
        List<String> dateJoin = new ArrayList<>();
        List<BigDecimal> purchaseCores = new ArrayList<>();
        list.stream().map(DataSplitTreeNode::getData).forEach((o) -> {
            dateJoin.add(Strings.join("@", o.getYearMonthStr(), o.getQuarter(), o.getHalfYear()));
            purchaseCores.add(o.getPurchaseCore().setScale(1, RoundingMode.HALF_UP));
        });
        tree.setDateJoin(dateJoin);
        tree.setPurchaseCore(purchaseCores);

        /*
        Map<String, BigDecimal> quarterMap = EStream.of(list).groupBy((o) -> o.getData().getQuarterName())
                .toMap(Entry::getKey, (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getData().getPurchaseCore()));
        EStream.of(dateColumList.getQuarterDate()).map((o) -> getFromMap(quarterMap, o))
                .consumeAsList(tree::setQuarterPurchaseCore);
        Map<String, BigDecimal> halfYearMap = EStream.of(list).groupBy((o) -> o.getData().getHalfYearName())
                .toMap(Entry::getKey, (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getData().getPurchaseCore()));
        EStream.of(dateColumList.getHalfYearDate()).map((o) -> getFromMap(halfYearMap, o))
                .consumeAsList(tree::setHalfYearPurchaseCore);
        Map<String, BigDecimal> monthMap = EStream.of(list).groupBy((o) -> o.getData().getYearMonthStr())
                .toMap(Entry::getKey, (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getData().getPurchaseCore()));
        EStream.of(dateColumList.getMonthDate()).map((o) -> getFromMap(monthMap, o))
                .consumeAsList(tree::setMonthPurchaseCore);
        */
        tree.setMsg(list.get(0).getMsg());
    }

    private static void setNumInfo3(Tree3 tree, List<DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO>> list,
            DateColumList dateColumList) {

        list.sort(Comparator.comparing((o) -> o.getData().getYearMonthStr()));
        List<String> dateJoin = new ArrayList<>();
        List<BigDecimal> purchaseCores = new ArrayList<>();
        list.stream().map(DataSplitTreeNode::getData).forEach((o) -> {
            dateJoin.add(Strings.join("@", o.getYearMonthStr(), o.getQuarter(), o.getHalfYear()));
            purchaseCores.add(o.getPurchaseCore().setScale(1, RoundingMode.HALF_UP));
        });

        /*
        Map<String, BigDecimal> quarterMap = EStream.of(list).groupBy((o) -> o.getData().getQuarterName())
                .toMap(Entry::getKey, (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getData().getPurchaseCore()));
        EStream.of(dateColumList.getQuarterDate()).map((o) -> getFromMap(quarterMap, o))
                .consumeAsList(tree::setQuarterPurchaseCore);
        Map<String, BigDecimal> halfYearMap = EStream.of(list).groupBy((o) -> o.getData().getHalfYearName())
                .toMap(Entry::getKey, (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getData().getPurchaseCore()));
        EStream.of(dateColumList.getHalfYearDate()).map((o) -> getFromMap(halfYearMap, o))
                .consumeAsList(tree::setHalfYearPurchaseCore);
        Map<String, BigDecimal> monthMap = EStream.of(list).groupBy((o) -> o.getData().getYearMonthStr())
                .toMap(Entry::getKey, (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getData().getPurchaseCore()));
        EStream.of(dateColumList.getMonthDate()).map((o) -> getFromMap(monthMap, o))
                .consumeAsList(tree::setMonthPurchaseCore);
         */

        tree.setDateJoin(dateJoin);
        tree.setPurchaseCore(purchaseCores);
        if (list.stream().anyMatch((o) -> o.getData().getAutoIncrId() != null)) {
            tree.setUpdateId(list.stream().map((o) -> o.getData().getAutoIncrId()).collect(Collectors.toList()));
        }
        tree.setMsg(list.get(0).getMsg());
    }


    @SneakyThrows
    @SuppressWarnings("SpringTransactionalMethodCallsInspection")
    private OriginTrees getOriginTrees(CreateSplitVersionReq req) {
        if (LongtermPredictOutputSplitVersionDO.db().isExist("where task_id=? and  name=?",
                req.getTaskId(), req.getName())) {
            throw new RuntimeException("数据拆分版本名称已存在: " + req.getName());
        }
        LongtermPredictTaskDO taskDO = LongtermPredictTaskDO.db().getOne("where id=?", req.getTaskId());
        LongtermPredictOutputSplitVersionDO splitVersion = new LongtermPredictOutputSplitVersionDO();
        splitVersion.setTaskId(req.getTaskId());
        splitVersion.setNote(JSON.toJson(req.getNote()));
        splitVersion.setName(req.getName());
        splitVersion.setCreator(LoginUtils.getUserNameWithSystem());
        splitVersion.setSplitArg(JSON.toJsonFormatted(req));

        checkSplitConfig(req);

        LongtermPredictOutputSplitVersionDO.db().insert(splitVersion);

        CompletableFuture<List<ScaleDataSplit>> scaleSplitTreesFuture = CompletableFuture.supplyAsync(
                () -> this.splitScale(splitVersion, req));
        CompletableFuture<List<IndustryDeptPurchaseDataSplit>> purchaseSplitTreesFuture = CompletableFuture.supplyAsync(
                () -> this.splitIndustryDeptPurchase(splitVersion, req));
        CompletableFuture<List<DeviceTypePurchaseDataSplit>> deviceSplitTreesFuture = CompletableFuture.supplyAsync(
                () -> this.splitDevicePurchase(splitVersion, req));

        List<ScaleDataSplit> scaleSplitTrees = scaleSplitTreesFuture.get();
        List<IndustryDeptPurchaseDataSplit> industryDeptSplitTrees = purchaseSplitTreesFuture.get();
        List<DeviceTypePurchaseDataSplit> deviceSplitTrees = deviceSplitTreesFuture.get();
        LongtermPredictOutputSplitVersionDO.db().update(splitVersion);

        List<LongtermPredictOutputScaleSplitDO> scaleSplit = scaleSplitTrees.stream()
                .flatMap((o) -> o.getAfterSplitData().stream()).collect(Collectors.toList());
        List<LongtermPredictOutputPurchaseSplitDO> deviceSplit = deviceSplitTrees.stream()
                .flatMap((o) -> o.getAfterSplitData().stream()).collect(Collectors.toList());
        setAutoIncrId(deviceSplit);
        setInstanceTypeFromDeviceType(deviceSplit);
        List<LongtermPredictOutputPurchaseSplitIndustryDeptDO> industryDeptSplit = industryDeptSplitTrees.stream()
                .flatMap((o) -> o.getAfterSplitData().stream()).collect(Collectors.toList());

        List<LongtermPredictOutputPurchaseSplitTreeDO> deviceSplitTreeDO = Lang.list();
        for (int i = 0; i < deviceSplitTrees.size(); i++) {
            DeviceTypePurchaseDataSplit tree = deviceSplitTrees.get(i);
            DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> cur = tree.getRoot().getChildren().get(0);
            List<LongtermPredictOutputPurchaseSplitTreeDO> oneTree =
                    dfsPurchase(cur, i + 1, new AtomicLong(1), 0, 1);
            deviceSplitTreeDO.addAll(oneTree);
        }
        List<LongtermPredictOutputScaleSplitTreeDO> scaleSplitTreeDO = Lang.list();
        for (int i = 0; i < scaleSplitTrees.size(); i++) {
            ScaleDataSplit tree = scaleSplitTrees.get(i);
            List<LongtermPredictOutputScaleSplitTreeDO> oneTree
                    = dfsScale(tree.getRoot().getChildren().get(0), i + 1, new AtomicLong(1), 0, 1);
            scaleSplitTreeDO.addAll(oneTree);
        }
        List<LongtermPredictOutputPurchaseSplitIndustryDeptTreeDO> industrySplitTreeDO = Lang.list();
        for (int i = 0; i < scaleSplitTrees.size(); i++) {
            IndustryDeptPurchaseDataSplit tree = industryDeptSplitTrees.get(i);
            List<LongtermPredictOutputPurchaseSplitIndustryDeptTreeDO> oneTree
                    = dfsIndustry(tree.getRoot().getChildren().get(0), i + 1, new AtomicLong(1), 0, 1);
            industrySplitTreeDO.addAll(oneTree);
        }

        Long versionId = splitVersion.getId();
        CompletableFuture<Boolean> insert1 =
                insetAsync(LongtermPredictOutputScaleSplitDO.class, scaleSplit, versionId);
        CompletableFuture<Boolean> insert11 =
                insetAsync(LongtermPredictOutputScaleSplitTreeDO.class, scaleSplitTreeDO, versionId);
        CompletableFuture<Boolean> insert2 =
                insetAsync(LongtermPredictOutputPurchaseSplitDO.class, deviceSplit, versionId);
        CompletableFuture<Boolean> insert21 =
                insetAsync(LongtermPredictOutputPurchaseSplitTreeDO.class, deviceSplitTreeDO, versionId);
        CompletableFuture<Boolean> insert3 =
                insetAsync(LongtermPredictOutputPurchaseSplitIndustryDeptDO.class, industryDeptSplit, versionId);
        CompletableFuture<Boolean> insert31 =
                insetAsync(LongtermPredictOutputPurchaseSplitIndustryDeptTreeDO.class, industrySplitTreeDO, versionId);
        trySaveAdjust(taskDO, versionId, deviceSplit);
        if (!insert1.get() || !insert11.get() || !insert2.get() || !insert21.get() || !insert3.get()
                || !insert31.get()) {
            throw BizException.makeThrow("插入数据异常，请重试");
        }
        return new OriginTrees(scaleSplitTrees, deviceSplitTrees, industryDeptSplitTrees, versionId);
    }

    private void setInstanceTypeFromDeviceType(List<LongtermPredictOutputPurchaseSplitDO> deviceSplit) {
        Map<String, String> deviceTypeToInstanceTypeMap = longtermPredictDictService.getDeviceTypeToInstanceTypeMap();
        deviceSplit.forEach((o) -> o.setInstanceTypeFromDeviceType(
                deviceTypeToInstanceTypeMap.getOrDefault(o.getDeviceType(), "(空值)")));
    }

    private void trySaveAdjust(LongtermPredictTaskDO taskDO, Long versionId,
            List<LongtermPredictOutputPurchaseSplitDO> deviceSplit) {
        // 如果是业务决策这里插入到adjust表中
        List<LongtermPredictOutputPurchaseSplitAdjustDO> adjustDOS = taskDO.isDecisionCategory() ?
                transToAnnualService.tranFrom(deviceSplit) : Lang.list();
        adjustDOS.forEach((o) -> o.setSplitVersionId(versionId));

        try {
            // 不管有没有成功，都先不处理， 里层会删除数据
            insetAsync(LongtermPredictOutputPurchaseSplitAdjustDO.class, adjustDOS, versionId).get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        } catch (Exception ignore) {
        }
    }

    private List<LongtermPredictOutputPurchaseSplitTreeDO> dfsPurchase(
            DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> cur,
            long treeIndex, AtomicLong id, long parentId, long level) {
        LongtermPredictOutputPurchaseSplitDO data = cur.getData();
        LongtermPredictOutputPurchaseSplitTreeDO treeDO = new LongtermPredictOutputPurchaseSplitTreeDO();
        List<LongtermPredictOutputPurchaseSplitTreeDO> rst = Lang.list();
        rst.add(treeDO);
        long curTreeId = id.getAndIncrement();
        BeanUtils.copyProperties(data, treeDO);
        treeDO.cleanBaseDO();
        treeDO.setTreeParent(parentId);
        treeDO.setTreeLevel(level);
        treeDO.setTreeIndex(treeIndex);
        treeDO.setTreeId(curTreeId);
        treeDO.setTreeNote(cur.getMsg());
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitDO> child : cur.getChildren()) {
            rst.addAll(dfsPurchase(child, treeIndex, id, curTreeId, level + 1));
        }
        return rst;
    }

    private List<LongtermPredictOutputScaleSplitTreeDO> dfsScale(
            DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> cur,
            long treeIndex, AtomicLong id, long parentId, long level) {
        LongtermPredictOutputScaleSplitDO data = cur.getData();
        LongtermPredictOutputScaleSplitTreeDO treeDO = new LongtermPredictOutputScaleSplitTreeDO();
        List<LongtermPredictOutputScaleSplitTreeDO> rst = Lang.list(treeDO);
        long curTreeId = id.getAndIncrement();
        BeanUtils.copyProperties(data, treeDO);
        treeDO.cleanBaseDO();
        treeDO.setTreeParent(parentId);
        treeDO.setTreeLevel(level);
        treeDO.setTreeIndex(treeIndex);
        treeDO.setTreeId(curTreeId);
        treeDO.setTreeNote(cur.getMsg());
        for (DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> child : cur.getChildren()) {
            rst.addAll(dfsScale(child, treeIndex, id, curTreeId, level + 1));
        }
        return rst;
    }

    private List<LongtermPredictOutputPurchaseSplitIndustryDeptTreeDO> dfsIndustry(
            DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> cur,
            long treeIndex, AtomicLong id, long parentId, long level) {
        LongtermPredictOutputPurchaseSplitIndustryDeptDO data = cur.getData();
        LongtermPredictOutputPurchaseSplitIndustryDeptTreeDO treeDO
                = new LongtermPredictOutputPurchaseSplitIndustryDeptTreeDO();
        List<LongtermPredictOutputPurchaseSplitIndustryDeptTreeDO> rst = Lang.list(treeDO);
        long curTreeId = id.getAndIncrement();
        BeanUtils.copyProperties(data, treeDO);
        treeDO.cleanBaseDO();
        treeDO.setTreeParent(parentId);
        treeDO.setTreeLevel(level);
        treeDO.setTreeIndex(treeIndex);
        treeDO.setTreeId(curTreeId);
        treeDO.setTreeNote(cur.getMsg());
        for (DataSplitTreeNode<LongtermPredictOutputPurchaseSplitIndustryDeptDO> child : cur.getChildren()) {
            rst.addAll(dfsIndustry(child, treeIndex, id, curTreeId, level + 1));
        }
        return rst;
    }

    // todo 检查虚拟机，层级关系
    @SuppressWarnings("Convert2MethodRef")
    private static void checkSplitConfig(CreateSplitVersionReq req) {
        // check sum == 100%
        ErrorUtil.throwIfNull(req.getCustomhouseTitleRate(), "境内外拆分比例为null");
        ErrorUtil.throwIfNull(req.getBizRangeTypeRate(), "内外部拆分比例为null");
        ErrorUtil.throwIfNull(req.getInstanceFamilyRate(), "拆分实例大类的比例为null");
        ErrorUtil.throwIfNull(req.getInstanceFamilyToDeviceTypeRate(), "拆分物理机的比例为null");
        ErrorUtil.throwIfNull(req.getInstanceFamilyToZoneNameRate(), "拆分可用区的比例为null");
        ErrorUtil.throwIfNull(req.getMonthRate(), "拆分年月的比例为null");

        EStream.of(req.getCustomhouseTitleRate())
                .groupBy((o) -> "TOTAL").forEach((kv) -> {
                    String info = "境内外拆分比例不为 100%%,请检查配置";
                    throwExceptionIfSumNotEqualOne(kv.getValue(), info);
                });
        EStream.of(req.getBizRangeTypeRate())
                .groupBy((o) -> "TOTAL").forEach((kv) -> {
                    String info = "内外部拆分比例不为 100%%,请检查配置";
                    throwExceptionIfSumNotEqualOne(kv.getValue(), info);
                });
        EStream.of(req.getInstanceFamilyRate())
                .groupBy((o) -> o.splitInstanceFamilyPrefixKey()).forEach((kv) -> {
                    String info = String.format("境内外：【%s】拆分实例大类的比例不为 100%%,请检查配置", kv.getKey());
                    throwExceptionIfSumNotEqualOne(kv.getValue(), info);
                });
        EStream.of(req.getInstanceFamilyToDeviceTypeRate())
                .groupBy((o) -> o.splitDeviceTypePrefixKey()).forEach((kv) -> {
                    String info = String.format("境内外+实例大类：【%s】拆分物理机的比例不为 100%%,请检查配置", kv.getKey());
                    throwExceptionIfSumNotEqualOne(kv.getValue(), info);

                    // 对年月的比例进行校验
                    Map<String, BigDecimal> ymMap = new HashMap<>();
                    List<InstanceFamilyToDeviceTypeRate> value = kv.getValue();
                    for (InstanceFamilyToDeviceTypeRate a : value) {
                        if (a.getYearMonthItemList() == null) {
                            continue;
                        }
                        for (YearMonthItem b : a.getYearMonthItemList()) {
                            String key = b.getYm() + a.getInstanceFamily();
                            if (b.getRate() != null) {
                                if (!ymMap.containsKey(key)) {
                                    ymMap.put(key, BigDecimal.ZERO);
                                }
                                ymMap.put(key, ymMap.get(key).add(b.getRate()));
                            } else {
                                if (ymMap.containsKey(key)) {
                                    throw BizException.makeThrow("拆分物理机的月特定比例中,维度:%s %s月份存在 null值",
                                            kv.getKey(), b.getYm().toString());
                                }
                            }
                        }
                    }
                    ymMap.forEach((k, v) -> {
                        boolean isOne = v.subtract(BigDecimal.ONE).abs().compareTo(BigDecimal.valueOf(0.01)) < 0;
                        if (!isOne) {
                            throw BizException.makeThrow("拆分物理机的月特定比例中,维度:%s-%s月份的比例不为 100%%,请检查配置",
                                    kv.getKey(), k);
                        }
                    });

                });
        EStream.of(req.getInstanceFamilyToZoneNameRate())
                .groupBy((o) -> o.splitZoneNamePrefixKey()).forEach((kv) -> {
                    String info = String.format("境内外+实例大类：【%s】拆分可用区的比例不为 100%%,请检查配置", kv.getKey());
                    throwExceptionIfSumNotEqualOne(kv.getValue(), info);
                });
        EStream.of(req.getMonthRate())
                .groupBy((o) -> o.splitMonthPrefixKey()).forEach((kv) -> {
                    String info = String.format("境内外：【%s】拆分年月比的例不为 100%% ,  请检查配置", kv.getKey());
                    throwExceptionIfSumNotEqualOne(kv.getValue(), info);
                });
    }

    private static void throwExceptionIfSumNotEqualOne(List<? extends RateGetter> rates, String info) {
        BigDecimal sum = NumberUtils.sum(rates, RateGetter::getRate);
        boolean isOne = sum.subtract(BigDecimal.ONE).abs().compareTo(BigDecimal.valueOf(0.01)) < 0;
        if (!isOne) {
            throw new BizException(info);
        }
    }

    private static <T> CompletableFuture<Boolean> insetAsync(Class<T> clazz, List<T> deviceSplit, Long splitVersionId) {
        return CompletableFuture.supplyAsync(() -> insetBatch(clazz, deviceSplit, splitVersionId));
    }

    private static <T> boolean insetBatch(Class<T> clazz, List<T> deviceSplit, Long splitVersionId) {
        AtomicInteger successInsert = new AtomicInteger();
        try {
            // 优化插入的性能
            ListUtils.partition(deviceSplit, 10000).stream()
                    .map((list) ->
                            CompletableFuture.supplyAsync(() -> DBList.cdLabDbHelper.insertBatchWithoutReturnId(list))
                    )
                    .collect(Collectors.toList())
                    .forEach((o) -> {
                        try {
                            successInsert.addAndGet(o.get());
                        } catch (InterruptedException | ExecutionException e) {
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        // 这里尽可能去删除数据，没删除也不影响
        finally {
            if (deviceSplit.size() != successInsert.get()) {
                CompletableFuture.runAsync(
                        () -> DBList.cdLabDbHelper.delete(clazz, "where split_version_id=?", splitVersionId));
            }
        }
        return deviceSplit.size() == successInsert.get();
    }


    @SneakyThrows
    private void setAutoIncrId(List<LongtermPredictOutputPurchaseSplitDO> deviceSplit) {
        int cnt = 0;
        List<Long> autoIncrementIdBatch = null;
        while (cnt++ < 5) {
            autoIncrementIdBatch = redisHelper.getAutoIncrementIdBatch("longterm-split", deviceSplit.size());
            if (autoIncrementIdBatch != null) {
                break;
            }
            Thread.sleep(500 + cnt * 1000L);
        }
        if (autoIncrementIdBatch == null) {
            throw new RuntimeException("获取自增id失败");
        }
        List<Long> finalAutoIncrementIdBatch = autoIncrementIdBatch;
        IntStream.range(0, deviceSplit.size()).forEach(i -> {
            deviceSplit.get(i).setAutoIncrId(finalAutoIncrementIdBatch.get(i));
        });
    }

    private static class OriginTrees {


        public List<ScaleDataSplit> scaleSplitTrees;
        public List<DeviceTypePurchaseDataSplit> deviceTypeSplitTrees;
        public List<IndustryDeptPurchaseDataSplit> industrySplitTrees;
        public Long splitVersionId;

        public OriginTrees(List<ScaleDataSplit> scaleSplitTrees, List<DeviceTypePurchaseDataSplit> deviceSplitTrees,
                List<IndustryDeptPurchaseDataSplit> purchaseSplitTrees, Long splitVersionId) {
            this.scaleSplitTrees = scaleSplitTrees;
            this.deviceTypeSplitTrees = deviceSplitTrees;
            this.industrySplitTrees = purchaseSplitTrees;
            this.splitVersionId = splitVersionId;
        }

    }

    @Override
    @Transactional(value = "cdlabTransactionManager")
    public List<ScaleDataSplit> splitScale(LongtermPredictOutputSplitVersionDO splitVersion,
            CreateSplitVersionReq req) {

        ScaleSplitDependDTO scaleSplitDependDTO = getHistory(splitVersion.getTaskId());
        scaleSplitDependDTO.setCustomhouseTitleRate(req.getCustomhouseTitleRate());
        scaleSplitDependDTO.setBizRangeTypeRate(req.getBizRangeTypeRate());
        List<LongtermPredictOutputScaleDO> totalOriginData = scaleSplitDependDTO.getTotalOriginData();
        Function<LongtermPredictOutputScaleDO, String> regionKeyFunc =
                o -> Strings.join("@", o.getRegionName(), o.getCountryName());
        Map<Long, LongtermPredictInputArgsDO> inputArgsDOMap =
                ListUtils2.toMap(scaleSplitDependDTO.getAllInputArgs(), BaseDO::getId);

        List<ScaleDataSplit> trees = Collections.synchronizedList(new ArrayList<>());

        ListUtils2.groupThenAccept(totalOriginData, LongtermPredictOutputScaleDO::getStrategyType, (strategy, list) -> {
            // 每个策略下，现在新增很多region 信息
            ListUtils2.groupThenAccept(list, regionKeyFunc, (regionKey, dateList) -> {
                dateList.sort(Comparator.comparing(LongtermPredictOutputScaleDO::getStatTime));
                ScaleDataSplit lastTree = null;
                for (LongtermPredictOutputScaleDO originData : dateList) {
                    ScaleDataSplit tree = new ScaleDataSplit("root-根节点没有data");
                    DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> node
                            = DataSplitTreeNode.newNode("原始数据", originData.toSplit());
                    node.getData().setSplitVersionId(splitVersion.getId());
                    tree.addNode(tree.getRoot(), node);
                    ScaleDataSplitOrder splitOrder = new ScaleDataSplitOrder();
                    tree.setSplitOrder(splitOrder);
                    tree.setCurInputArgs(inputArgsDOMap.get(originData.getInputArgsId()));
                    tree.setScaleSplitDependDTO(scaleSplitDependDTO);

                    final ScaleDataSplit lastTreeFinal = lastTree;
                    splitOrder.forEachRemaining((o) -> {
                        log.info("开始拆分存量: {}", o.getName());
                        if (Strings.equals(o.getName(), ScaleDataSplitOrder.MONTH_SPLIT)) {
                            splitMonth(tree, originData);
                        } else if (Strings.equals(o.getName(), ScaleDataSplitOrder.CUSTOM_HOUSE_TITLE_SPLIT)) {
                            splitCustomhouseTitleByConfig(lastTreeFinal, tree);
                        } else if (Strings.equals(o.getName(), ScaleDataSplitOrder.BIZ_RANGE_TYPE_SPLIT)) {
                            if (scaleSplitDependDTO.getTaskDO().isDecisionCategory()) {
                                splitBizRangeTypeByConfig1(lastTreeFinal, tree);
                            } else {
                                splitBizRangeTypeByConfig(lastTreeFinal, tree);
                            }
                        }
                        else if (Strings.equals(o.getName(), ScaleDataSplitOrder.REGION_SPLIT_NAME)) {
                            splitColumnInfo(tree);
                        }
                        else if (Strings.equals(o.getName(), ScaleDataSplitOrder.INSTANCE_TYPE)) {
                            splitColumnInfo(tree);
                        }else {
                            throw BizException.makeThrow("不支持拆分类型: {}", o.getName());
                        }

                    });
                    // 拆分后，向上设置区域信息
                    setNotSplitColumnInfo(tree.getAfterSplitData(), splitVersion.getId());
                    lastTree = tree;
                    trees.add(tree);
                }
            });
        });

        return trees;
    }

    private void splitBizRangeTypeByConfig1(ScaleDataSplit lastTree, ScaleDataSplit tree) {

        List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> leafLevel = tree.getLeafLevel();

        String strategyType = tree.getCurInputArgs().getStrategyType();
        List<LongtermPredictInputScaleDO> inputScaleDetail
                = getInputOrOutputScaleSub1M(tree.getScaleSplitDependDTO().getTaskDO(), strategyType);

        // 按时间排序从早到晚排序
        leafLevel.sort(Comparator.comparing(o -> o.getData().getStatTime()));
        for (int i = 0; i < leafLevel.size(); i++) {
            DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> oneLeaf = leafLevel.get(i);
            LongtermPredictOutputScaleSplitDO needSplit = oneLeaf.getData();
            List<LongtermPredictInputScaleDO> historyDetail = EStream.of(inputScaleDetail).filter((o) -> {
                if (Strings.isNotBlank(needSplit.getRegionName())) {
                    return Strings.equals(o.getRegionName(), needSplit.getRegionName());
                } else if (Strings.isNotBlank(needSplit.getCountryName())) {
                    return Strings.equals(o.getCountryName(), needSplit.getCountryName());
                } else {
                    throw BizException.makeThrow("regionName和RegionName 都是空的");
                }
            }).toList();

            BigDecimal lastInnerCur;
            BigDecimal lastOutCur;
            // 首先获取上一个月的存量占比数据
            if (i == 0) {
                // 从历史数据获取
                if (lastTree == null) {
                    lastInnerCur = filterByNameAndSumCur(historyDetail, "内部业务");
                    lastOutCur = filterByNameAndSumCur(historyDetail, "外部业务");
                } else {
                    // 获取list 的最后4个元素, 拆分的境内外
                    List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> curLevel
                            = lastTree.getLevels().get(3);
                    curLevel.sort(Comparator.comparing((DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> o)
                                    -> o.getData().getYearMonthStr())
                            .reversed()
                            .thenComparing(o -> o.getData().getBizRangeType())
                    );
                    List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> io = curLevel.subList(0, 2);
                    lastInnerCur = io.get(0).getData().getCurCore();
                    lastOutCur = io.get(1).getData().getCurCore();
                }
            } else {
                // 从 i-1 获取数据
                DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> node = leafLevel.get(i - 1);
                List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> children = node.getChildren();
                ErrorUtil.throwIfFalse(children.size() == 2, "拆分内外部时,上级节点的子节点不为2,请检查");
                // 如果有境内外，不保证顺序
                LongtermPredictOutputScaleSplitDO lastInner = EStream.of(children)
                        .map(DataSplitTreeNode::getData)
                        .filter((o) -> Strings.equals(o.getBizRangeType(), "内部业务")).toList().get(0);
                LongtermPredictOutputScaleSplitDO lastOut = EStream.of(children)
                        .map(DataSplitTreeNode::getData)
                        .filter((o) -> Strings.equals(o.getBizRangeType(), "外部业务")).toList().get(0);
                lastInnerCur = lastInner.getCurCore();
                lastOutCur = lastOut.getCurCore();
            }

            List<BizRangeTypeRate> dbConfig = tree.getScaleSplitDependDTO().getBizRangeTypeRate();
            BizRangeTypeRate innerConfig = EStream.of(dbConfig)
                    .filter((o) -> Strings.equals(o.getBizRangeType(), "内部业务")).toList().get(0);
            BizRangeTypeRate outConfig = EStream.of(dbConfig)
                    .filter((o) -> Strings.equals(o.getBizRangeType(), "外部业务")).toList().get(0);
            BigDecimal diff = needSplit.getCurCore().subtract(needSplit.getStartCurCore()); // 总的增量

            BigDecimal innerDiff = diff.multiply(innerConfig.getRate());
            BigDecimal outDiff = diff.multiply(outConfig.getRate());
            BigDecimal innerCur = lastInnerCur.add(innerDiff);
            BigDecimal outCur = lastOutCur.add(outDiff);

            tree.addNode(oneLeaf, getBizRangeTypeNode(needSplit, "内部业务", innerCur, lastInnerCur));
            tree.addNode(oneLeaf, getBizRangeTypeNode(needSplit, "外部业务", outCur, lastOutCur));
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), needSplit.getCurCore());
        }
    }


    private void splitBizRangeTypeByConfig(ScaleDataSplit lastTree, ScaleDataSplit tree) {

        List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> leafLevel = tree.getLeafLevel();

        String strategyType = tree.getCurInputArgs().getStrategyType();
        List<LongtermPredictInputScaleDO> inputScaleDetail
                = getInputOrOutputScaleSub1M(tree.getScaleSplitDependDTO().getTaskDO(), strategyType);

        Function<LongtermPredictInputScaleDO, Boolean> isInner = o -> Strings.equals("境内", o.getCustomhouseTitle());
        Map<Boolean, List<LongtermPredictInputScaleDO>> ctGroupBy = ListUtils2.group(inputScaleDetail, isInner);

        ListUtils2.groupThenAccept(leafLevel, (o) -> o.getData().getCustomhouseTitle(), (ct, dateList) -> {
            // 按时间排序从早到晚排序
            dateList.sort(Comparator.comparing(o -> o.getData().getStatTime()));
            for (int i = 0; i < dateList.size(); i++) {
                DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> oneLeaf = dateList.get(i);
                LongtermPredictOutputScaleSplitDO needSplit = oneLeaf.getData();
                List<LongtermPredictInputScaleDO> historyDetail
                        = ctGroupBy.get(Strings.equals("境内", needSplit.getCustomhouseTitle()));

                BigDecimal lastInnerCur;
                BigDecimal lastOutCur;
                // 首先获取上一个月的存量占比数据
                if (i == 0) {
                    // 从历史数据获取
                    if (lastTree == null) {
                        // todo
                        lastInnerCur = filterByNameAndSumCur(historyDetail, "内部业务");
                        lastOutCur = filterByNameAndSumCur(historyDetail, "外部业务");
                    } else {
                        // todo
                        // 获取list 的最后4个元素, 拆分的境内外
                        List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> curLevel
                                = lastTree.getLevels().get(4);
                        // 按时间倒叙, 国家正序, 内外部正序
                        curLevel.sort(
                                Comparator.comparing(
                                                (DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> o)
                                                        -> o.getData().getYearMonthStr())
                                        .reversed()
                                        .thenComparing(o -> o.getData().getCustomhouseTitle())
                                        .thenComparing(o -> o.getData().getBizRangeType())
                        );
                        List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> io
                                = curLevel.subList(0, 4);
                        // 境内-内部  境内-外部   境外-内部 境外-外部
                        LongtermPredictOutputScaleSplitDO lastInner =
                                Strings.equals("境内", needSplit.getCustomhouseTitle()) ?
                                        io.get(0).getData() : io.get(2).getData();
                        LongtermPredictOutputScaleSplitDO lastOut =
                                Strings.equals("境外", needSplit.getCustomhouseTitle()) ?
                                        io.get(3).getData() : io.get(1).getData();
                        lastInnerCur = lastInner.getCurCore();
                        lastOutCur = lastOut.getCurCore();
                    }
                } else {
                    // 从 i-1 获取数据
                    DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> node = dateList.get(i - 1);
                    List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> children = node.getChildren();
                    ErrorUtil.throwIfFalse(children.size() == 2, "拆分内外部时,上级节点的子节点不为2,请检查");
                    LongtermPredictOutputScaleSplitDO lastInner = EStream.of(children)
                            .map(DataSplitTreeNode::getData)
                            .filter((o) -> Strings.equals(o.getBizRangeType(), "内部业务")).toList().get(0);
                    LongtermPredictOutputScaleSplitDO lastOut = EStream.of(children)
                            .map(DataSplitTreeNode::getData)
                            .filter((o) -> Strings.equals(o.getBizRangeType(), "外部业务")).toList().get(0);
                    lastInnerCur = lastInner.getCurCore();
                    lastOutCur = lastOut.getCurCore();
                }

                List<BizRangeTypeRate> dbConfig = tree.getScaleSplitDependDTO().getBizRangeTypeRate();
                BizRangeTypeRate innerConfig = EStream.of(dbConfig)
                        .filter((o) -> Strings.equals(o.getBizRangeType(), "内部业务")).toList().get(0);
                BizRangeTypeRate outConfig = EStream.of(dbConfig)
                        .filter((o) -> Strings.equals(o.getBizRangeType(), "外部业务")).toList().get(0);
                BigDecimal diff = needSplit.getCurCore().subtract(needSplit.getStartCurCore()); // 总的增量

                BigDecimal innerDiff = diff.multiply(innerConfig.getRate());
                BigDecimal outDiff = diff.multiply(outConfig.getRate());
                BigDecimal innerCur = lastInnerCur.add(innerDiff);
                BigDecimal outCur = lastOutCur.add(outDiff);

                tree.addNode(oneLeaf, getBizRangeTypeNode(needSplit, "内部业务", innerCur, lastInnerCur));
                tree.addNode(oneLeaf, getBizRangeTypeNode(needSplit, "外部业务", outCur, lastOutCur));
                throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), needSplit.getCurCore());
            }
        });
    }

    private static BigDecimal filterByNameAndSumCur(
            List<LongtermPredictInputScaleDO> historyDetail, String bizRangeTypeName) {
        if (historyDetail == null) {
            return BigDecimal.ZERO;
        }
        return historyDetail.stream()
                .filter((o) -> o != null && Strings.equals(bizRangeTypeName, o.getBizRangeType()))
                .map(LongtermPredictInputScaleDO::getCurCore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private void splitCustomhouseTitleByConfig(ScaleDataSplit lastTree, ScaleDataSplit tree) {

        LongtermPredictTaskDO taskDO = tree.getScaleSplitDependDTO().getTaskDO();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
        Map<String, String> countryNameInfoMap = EStream.of(regionNameInfoMap.values())
                .toMap(SoeRegionNameCountryDO::getCountryName, SoeRegionNameCountryDO::getCustomhouseTitle,
                        (a, b) -> a);

        List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> leafLevel = tree.getLeafLevel();
        // 按时间排序从早到晚排序
        leafLevel.sort(Comparator.comparing(o -> o.getData().getStatTime()));

        String strategyType = tree.getCurInputArgs().getStrategyType();
        List<LongtermPredictInputScaleDO> inputScaleDetail
                = getInputOrOutputScaleSub1M(tree.getScaleSplitDependDTO().getTaskDO(), strategyType);

        for (int i = 0; i < leafLevel.size(); i++) {
            DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> oneLeaf = leafLevel.get(i);
            LongtermPredictOutputScaleSplitDO needSplit = oneLeaf.getData();

            if (taskDO.isDecisionCategory()) { // 只关联境内的数据
                setCustomhouseTitle(needSplit, regionNameInfoMap, countryNameInfoMap);
                continue;
            }

            BigDecimal lastInnerCur;
            BigDecimal lastOutCur;
            // 首先获取上一个月的存量占比数据
            if (i == 0) {
                // 从历史数据获取
                if (lastTree == null) {
                    lastInnerCur = inputScaleDetail.stream()
                            .filter((o) -> Strings.equals("境内", o.getCustomhouseTitle()))
                            .map(LongtermPredictInputScaleDO::getCurCore).reduce(BigDecimal.ZERO, BigDecimal::add);
                    lastOutCur = inputScaleDetail.stream()
                            .filter((o) -> !Strings.equals("境内", o.getCustomhouseTitle()))
                            .map(LongtermPredictInputScaleDO::getCurCore).reduce(BigDecimal.ZERO, BigDecimal::add);
                } else {
                    // 获取list 的最后2个元素, 拆分的境内外
                    List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> curLevel = lastTree.getLevels().get(3);
                    int size = curLevel.size();
                    List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> io = curLevel.subList(size - 2, size);
                    LongtermPredictOutputScaleSplitDO lastInner = io.get(0).getData();
                    LongtermPredictOutputScaleSplitDO lastOut = io.get(1).getData();
                    lastInnerCur = lastInner.getCurCore();
                    lastOutCur = lastOut.getCurCore();
                }
            } else {
                // 从 i-1 获取数据
                DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> node = leafLevel.get(i - 1);
                List<DataSplitTreeNode<LongtermPredictOutputScaleSplitDO>> children = node.getChildren();
                ErrorUtil.throwIfFalse(children.size() == 2, "拆分境内外时,上级节点的子节点不为2,请检查");
                LongtermPredictOutputScaleSplitDO lastInner = EStream.of(children).map(DataSplitTreeNode::getData)
                        .filter((o) -> Strings.equals(o.getCustomhouseTitle(), "境内")).toList().get(0);
                LongtermPredictOutputScaleSplitDO lastOut = EStream.of(children).map(DataSplitTreeNode::getData)
                        .filter((o) -> !Strings.equals(o.getCustomhouseTitle(), "境内")).toList().get(0);
                lastInnerCur = lastInner.getCurCore();
                lastOutCur = lastOut.getCurCore();
            }

            List<CustomhouseTitleRate> dbConfig = tree.getScaleSplitDependDTO().getCustomhouseTitleRate();
            CustomhouseTitleRate innerConfig = EStream.of(dbConfig)
                    .filter((o) -> Strings.equals(o.getCustomhouseTitle(), "境内")).toList().get(0);
            CustomhouseTitleRate outConfig = EStream.of(dbConfig)
                    .filter((o) -> !Strings.equals(o.getCustomhouseTitle(), "境内")).toList().get(0);
            BigDecimal diff = needSplit.getCurCore().subtract(needSplit.getStartCurCore()); // 总的增量

            BigDecimal innerDiff = diff.multiply(innerConfig.getRate());
            BigDecimal outDiff = diff.multiply(outConfig.getRate());
            BigDecimal innerCur = lastInnerCur.add(innerDiff);
            BigDecimal outCur = lastOutCur.add(outDiff);

            tree.addNode(oneLeaf, getNode(needSplit, "境内", innerCur, lastInnerCur));
            tree.addNode(oneLeaf, getNode(needSplit, "境外", outCur, lastOutCur));

            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(), needSplit.getCurCore());
        }
    }


    private static DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> getBizRangeTypeNode(
            LongtermPredictOutputScaleSplitDO needSplit,
            String bizRangeType,
            BigDecimal innerCur,
            BigDecimal lastInnerCur) {
        LongtermPredictOutputScaleSplitDO clone = JSON.clone(needSplit);
        clone.setBizRangeType(bizRangeType);
        clone.setCurCore(innerCur);
        clone.setStartCurCore(lastInnerCur);
        DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> node = DataSplitTreeNode.newNode("", clone);
        return node;
    }


    private static DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> getNode(
            LongtermPredictOutputScaleSplitDO needSplit,
            String customhouseTitle,
            BigDecimal innerCur,
            BigDecimal lastInnerCur) {
        LongtermPredictOutputScaleSplitDO clone = JSON.clone(needSplit);
        clone.setCustomhouseTitle(customhouseTitle);
        clone.setCurCore(innerCur);
        clone.setStartCurCore(lastInnerCur);
        DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> node = DataSplitTreeNode.newNode("", clone);
        return node;
    }


    @Override
    public List<Instance2DeviceRateResp> queryInstance2DeviceRate() {
        List<LongtermPredictInputInstance2deviceRateDO> all = self.getInstance2deviceRate();
        return all.stream().map((o) -> {
            Instance2DeviceRateResp one = new Instance2DeviceRateResp();
            one.setId(o.getId());
            InstanceToDeviceItemKey key = new InstanceToDeviceItemKey();
            key.setInstanceFamily(o.getInstanceFamily());
            key.setDeviceType(o.getDeviceType());
            key.setCustomhouseTitle(o.getCustomhouseTitle());
            one.setKey(key);
            one.setRate(o.getRate());
            return one;
        }).collect(Collectors.toList());
    }

    @Override
    public InstanceToDeviceResp uploadInstance2DeviceRateExcel(InstanceToDeviceReq req,
            List<InstanceToDeviceItem> excelDataList) {
        InstanceToDeviceResp resp = new InstanceToDeviceResp();
        List<InstanceToDeviceItemDiff> diffData = getDiffInfo(req.getWebData(), excelDataList);

        diffData.sort(Comparator.comparing((o) -> o.getKey().getCustomhouseTitle()));
        addWarningInfo(diffData, resp);
        resp.setDiffData(diffData);
        resp.setExcelData(excelDataList);
        return resp;
    }

    private static void deleteLeft(List<InstanceToDeviceItem> list2excel, String info,
            List<InstanceToDeviceItemDiff> diffData) {
        for (int i = 1; i < list2excel.size(); i++) {
            InstanceToDeviceItemDiff errorDiff = getDeleteType(list2excel.get(i), info);
            diffData.add(errorDiff);
        }
    }

    private static InstanceToDeviceItemDiff getInsertType(InstanceToDeviceItem excel) {
        InstanceToDeviceItemDiff cur = new InstanceToDeviceItemDiff();
        InstanceToDeviceItemDiff.setSuper(cur, excel);
        cur.setType("INSERT");
        cur.setInfo("新增");
        cur.setRate(BigDecimal.ZERO);
        cur.setRateAfterChange(excel.getRate());
        return cur;
    }

    private static InstanceToDeviceItemDiff getDeleteType(InstanceToDeviceItem list2excel, String info) {
        InstanceToDeviceItemDiff errorDiff = new InstanceToDeviceItemDiff();
        InstanceToDeviceItemDiff.setSuper(errorDiff, list2excel);
        errorDiff.setType("DELETE");
        errorDiff.setInfo(info);
        errorDiff.setRateAfterChange(null);
        return errorDiff;
    }


    @Override
    @Transactional(value = "cdlabTransactionManager")
    public ImmutableMap<String, Integer> updateSplitRstById(UpdateSplitRstReq req) {

        Long splitVersionId = req.getSplitVersionId();
        if (splitVersionId == null) {
            throw new RuntimeException("拆分版本id为空: " + null);
        }
        LongtermPredictOutputSplitVersionDO versionDO = LongtermPredictOutputSplitVersionDO.db()
                .getByKey(splitVersionId);
        if (versionDO == null) {
            throw new RuntimeException("未找到版本: " + splitVersionId);
        }
        versionDO.appendChangeLog(req);
        LongtermPredictOutputSplitVersionDO.db().update(versionDO);

        String s = generateUpdateSql(req);
        int i = LongtermPredictOutputPurchaseSplitDO.db().executeRaw(s);
        return ImmutableMap.of("successNum", i);
    }

    @SuppressWarnings("Convert2MethodRef")
    @Override
    public QueryDeviceTypeSplitDetailResp queryDeviceTypeSplitDetail(QueryDeviceTypeSplitDetailReq req) {

        String splitVersionId = req.getSplitVersionId();
        LongtermPredictOutputSplitVersionDO versionDO =
                cdLabDbHelper.getByKey(LongtermPredictOutputSplitVersionDO.class, req.getSplitVersionId());
        if (versionDO == null) {
            throw new RuntimeException("未找到拆分版本: " + splitVersionId);
        }
        LongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(LongtermPredictTaskDO.class, versionDO.getTaskId());
        if (taskDO == null) {
            throw new RuntimeException("未找到任务: " + versionDO.getTaskId());
        }
        Map<String, Integer> deviceTypeToCoreNumMap = longtermPredictDictService.getDeviceTypeToCoreNumMap();
        Map<String, String> zoneName2RegionName = longtermPredictDictService.getZoneName2RegionName();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = longtermPredictDictService.getRegionNameInfoMap();

        // 中长期二期，切换为adjust表
        if (taskDO.isDecisionCategory()) {
            List<LongtermPredictOutputPurchaseSplitAdjustDO> adjust = LongtermPredictOutputPurchaseSplitAdjustDO.db()
                    .getAll("where split_version_id=?", req.getSplitVersionId());
            QueryDeviceTypeSplitDetailResp resp = new QueryDeviceTypeSplitDetailResp();
            EStream.of(adjust).groupAndConsume((o) -> o.getStrategyType(),
                    (strategyType, list) -> {
                        DateColumList dateColumList = getColumList(list);
                        StrategyTypeItem strategyTypeItem = new StrategyTypeItem();
                        resp.getStrategyTypeItems().add(strategyTypeItem);
                        strategyTypeItem.setDateKey(dateColumList);
                        strategyTypeItem.setStrategyType(strategyType);
                        strategyTypeItem.setStrategyTypeName(StrategyTypeEnum.getNameByCode(strategyType));
                        Collection<AdjustItem> data = ListUtils2.groupThenApply(list, (o) -> o.getKeyWithOutDateStr(),
                                (keyStr, dateList) -> getDateInfo4(dateList, dateColumList)).values();
                        data.forEach((o) -> {
                            o.setDeviceCoreNum(deviceTypeToCoreNumMap.get(o.getDeviceType()));
                            String regionName = zoneName2RegionName.get(o.getZoneName());
                            SoeRegionNameCountryDO soeInfo = regionNameInfoMap.get(regionName);
                            if (soeInfo != null) {
                                o.setCustomhouseTitle(soeInfo.getCustomhouseTitle());
                                o.setCountryName(soeInfo.getCountryName());
                                o.setRegionName(regionName);
                                boolean inner = Objects.equals(soeInfo.getCustomhouseTitle(), "境内");
                                o.setRegionInfo(inner ? regionName : o.getCountryName());
                            }
                        });
                        strategyTypeItem.setItems(new ArrayList<>(data));
                    });
            resp.setDisplayKeyColumn(true);
            return resp;
        }

        List<LongtermPredictOutputPurchaseSplitDO> detail = LongtermPredictOutputPurchaseSplitDO.db()
                .getAll("where split_version_id=?", req.getSplitVersionId());
        QueryDeviceTypeSplitDetailResp resp = new QueryDeviceTypeSplitDetailResp();
        EStream.of(detail).groupAndConsume((o) -> o.getStrategyType(),
                (strategyType, list) -> {
                    DateColumList dateColumList = getColumList(list);

                    StrategyTypeItem strategyTypeItem = new StrategyTypeItem();
                    resp.getStrategyTypeItems().add(strategyTypeItem);
                    strategyTypeItem.setDateKey(dateColumList);

                    strategyTypeItem.setStrategyType(strategyType);
                    strategyTypeItem.setStrategyTypeName(StrategyTypeEnum.getNameByCode(strategyType));
                    Collection<Item> data = ListUtils2.groupThenApply(list, (o) -> o.getKeyWithOutDateStr(),
                            (keyStr, dateList) -> getDateInfo(dateList, dateColumList)).values();
                    data.forEach((o) -> o.setDeviceCoreNum(deviceTypeToCoreNumMap.get(o.getDeviceType())));
                    strategyTypeItem.setItems(new ArrayList<>(data));
                });
        return resp;
    }

    @SneakyThrows
    @Override
    @SuppressWarnings("Convert2MethodRef")
    public CreateSplitVersionTableResp querySplitData(TaskIdAndSplitIdReq req) {

        CreateSplitVersionTableResp resp = new CreateSplitVersionTableResp();
        CompletableFuture<List<LongtermPredictOutputPurchaseSplitDO>> deviceDbDataFuture =
                CompletableFuture.supplyAsync(() -> LongtermPredictOutputPurchaseSplitDO.db()
                        .getAll("where split_version_id=?", req.getSplitVersionId()));
        CompletableFuture<List<LongtermPredictOutputPurchaseSplitIndustryDeptDO>> deviceIndustryDbDataFuture =
                CompletableFuture.supplyAsync(() -> LongtermPredictOutputPurchaseSplitIndustryDeptDO.db()
                        .getAll("where split_version_id=?", req.getSplitVersionId()));
        CompletableFuture<List<LongtermPredictOutputScaleSplitDO>> scaleDbDataFuture =
                CompletableFuture.supplyAsync(() -> LongtermPredictOutputScaleSplitDO.db()
                        .getAll("where split_version_id=?", req.getSplitVersionId()));

        Map<String, Integer> deviceTypeToCoreNumMap = longtermPredictDictService.getDeviceTypeToCoreNumMap();

        EStream.of(deviceDbDataFuture.get()).groupAndConsume((o) -> o.getStrategyType(),
                (strategyType, list) -> {
                    CreateSplitVersionTableResp.Table3 strategyTypeItem = new CreateSplitVersionTableResp.Table3();
                    resp.getTable3().add(strategyTypeItem);
                    DateColumList dateColumList = getColumList(list);
                    strategyTypeItem.setDateColumList(dateColumList);
                    strategyTypeItem.setStrategyType(strategyType);
                    strategyTypeItem.setStrategyTypeName(StrategyTypeEnum.getNameByCode(strategyType));
                    Collection<Item3> data =
                            ListUtils2.groupThenApply(list, (o) -> o.getKeyWithOutDateStr(),
                                    (keyStr, dateList) -> getDateInfo3(dateList, dateColumList)).values();
                    data.forEach((o) -> o.setDeviceCoreNum(deviceTypeToCoreNumMap.get(o.getDeviceType())));
                    strategyTypeItem.setItems(new ArrayList<>(data));
                });

        List<LongtermPredictOutputPurchaseSplitIndustryDeptDO> deviceIndustryDbData = deviceIndustryDbDataFuture.get();
        EStream.of(deviceIndustryDbData).groupAndConsume((o) -> o.getStrategyType(),
                (strategyType, list) -> {
                    CreateSplitVersionTableResp.Table2 strategyTypeItem = new CreateSplitVersionTableResp.Table2();
                    resp.getTable2().add(strategyTypeItem);
                    DateColumList dateColumList = getColumListWithIndustr(list);
                    strategyTypeItem.setDateColumList(dateColumList);
                    strategyTypeItem.setStrategyType(strategyType);
                    strategyTypeItem.setStrategyTypeName(StrategyTypeEnum.getNameByCode(strategyType));
                    Collection<Item2> data =
                            ListUtils2.groupThenApply(list, (o) -> o.getKeyWithOutDateStr(),
                                    (keyStr, dateList) -> getDateInfo2(dateList, dateColumList)).values();
                    strategyTypeItem.setItems(new ArrayList<>(data));
                });

        List<LongtermPredictOutputScaleSplitDO> scaleDbData = scaleDbDataFuture.get();
        EStream.of(scaleDbData).groupAndConsume((o) -> o.getStrategyType(),
                (strategyType, list) -> {
                    CreateSplitVersionTableResp.Table1 strategyTypeItem = new CreateSplitVersionTableResp.Table1();
                    resp.getTable1().add(strategyTypeItem);
                    DateColumList dateColumList = getScaleColumList(list);
                    strategyTypeItem.setDateColumList(dateColumList);
                    strategyTypeItem.setStrategyType(strategyType);
                    strategyTypeItem.setStrategyTypeName(StrategyTypeEnum.getNameByCode(strategyType));
                    Collection<Item1> data =
                            ListUtils2.groupThenApply(list, (o) -> o.getKeyWithOutDateStr(),
                                    (keyStr, dateList) -> getDateInfo1(dateList, dateColumList)).values();
                    strategyTypeItem.setItems(new ArrayList<>(data));
                });

        return resp;
    }

    @Override
    public InstanceToDeviceResp saveInstance2DeviceRateToDbPreview(InstanceToDeviceReq req) {
        InstanceToDeviceResp resp = new InstanceToDeviceResp();
        List<InstanceToDeviceItem> webData = req.getWebData();
        List<InstanceToDeviceItem> dbData = cdLabDbHelper.getAll(LongtermPredictInputInstance2deviceRateDO.class)
                .stream().map(InstanceToDeviceItem::from).collect(Collectors.toList());
        List<InstanceToDeviceItemDiff> diffData = getDiffInfo(dbData, webData);
        diffData.sort(Comparator.comparing(InstanceToDeviceItem::getKey));

        resp.setDiffData(diffData);
        addWarningInfo(diffData, resp);
        return resp;
    }

    @Override
    public ConfigSplitRateResp queryConfigSplitRate(Long taskId) {
        ConfigSplitRateResp resp = new ConfigSplitRateResp();
        ErrorUtil.throwIfNull(taskId, "taskId is null");
        LongtermPredictTaskDO task = self.getTask(taskId);
        if (task == null) {
            throw BizException.makeThrow("taskId 不存在");
        }
        LocalDate predictStart = task.getPredictStart();
        LocalDate predictEnd = task.getPredictEnd();
        YearMonth startYm = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue());
        YearMonth endYm = YearMonth.of(predictEnd.getYear(), predictEnd.getMonthValue());
        CompletableFuture.allOf(
                CompletableFuture.runAsync(() -> resp.setCustomhouseTitleRate(
                        cdLabDbHelper.getAll(LongtermPredictConfigCumstomhouseTitleRateDO.class))),
                CompletableFuture.runAsync(() -> resp.setBizRangeTypeRate(
                        cdLabDbHelper.getAll(LongtermPredictConfigBizRangeTypeRateDO.class))),
                CompletableFuture.runAsync(() -> resp.setInstanceFamilyRate(
                        cdLabDbHelper.getAll(LongtermPredictConfigInstanceFamilyRateDO.class))),
                CompletableFuture.runAsync(() -> resp.setInstanceFamilyToZoneNameRate(
                        cdLabDbHelper.getAll(LongtermPredictConfigInstanceFamilyToZoneNameRateDO.class))),
                CompletableFuture.runAsync(() ->
                        {
                            List<LongtermPredictConfigInstanceFamilyToDeviceTypeRateDO> all =
                                    cdLabDbHelper.getAll(LongtermPredictConfigInstanceFamilyToDeviceTypeRateDO.class);
                            all.forEach((o) -> {
                                List<YearMonthItem> yearMonthItemList = o.getYearMonthItemList()
                                        .stream()
                                        .filter((i) -> !i.getYm().isBefore(startYm) && !i.getYm().isAfter(endYm))
                                        .collect(Collectors.toList());
                                o.setYearMonthItemList(yearMonthItemList);
                            });
                            resp.setInstanceFamilyToDeviceTypeRate(all);
                        }
                ),
                CompletableFuture.runAsync(() -> resp.setMonthRate(
                        cdLabDbHelper.getAll(LongtermPredictConfigMonthRateDO.class)))
        ).join();


        LongtermPredictCategoryConfigDO categoryConfigDO = cdLabDbHelper.getOne(LongtermPredictCategoryConfigDO.class,
                "where id=?", task.getCategoryId());
        // 默认拆分修改一下比例,特殊的比例不可以修改
        SplitConfig splitConfig = categoryConfigDO.getSplitConfig();
        if (splitConfig != null && Lang.isNotEmpty(splitConfig.getCustomhouseTitleRates())){
            resp.setCustomhouseTitleRate(splitConfig.getCustomhouseTitleRates()
                    .stream().map(o-> new LongtermPredictConfigCumstomhouseTitleRateDO(o.getCustomhouseTitle(), o.getRate()))
                    .collect(Collectors.toList()));
        }
        return resp;
    }

    @Override
    public List<InstanceFamilyToDeviceTypeRate> tansByTask(
            List<InstanceFamilyToDeviceTypeRateExcelDTO> excel, Long taskId) {
        LongtermPredictTaskDO task = self.getTask(taskId);
        LocalDate predictStart = task.getPredictStart();
        return EStream.of(excel).map((o) -> {
            InstanceFamilyToDeviceTypeRate one = new InstanceFamilyToDeviceTypeRate();
            one.setCustomhouseTitle(o.getCustomhouseTitle());
            one.setInstanceFamily(o.getInstanceFamily());
            one.setDeviceType(o.getDeviceType());
            one.setRate(o.getRate());
            List<YearMonthItem> yearMonthItems = new ArrayList<>();
            one.setYearMonthItemList(yearMonthItems);
            YearMonth startMonth = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue());
            for (int i = 0; i < 18; i++) {
                YearMonthItem yearMonthItem = new YearMonthItem();
                YearMonth currentMonth = startMonth.plusMonths(i);
                yearMonthItem.setYear(currentMonth.getYear());
                yearMonthItem.setMonth(currentMonth.getMonthValue());
                InstanceFamilyToDeviceTypeRateExcelDTO.setByNum(yearMonthItem, o, i);
                yearMonthItems.add(yearMonthItem);
            }
            return one;
        }).toList();
    }


    @Override
    public ConfigSplitRateDiffResp diffExcelConfigSplitRate(ConfigSplitRateDiffReq web, ConfigSplitRateDiffReq excel) {
        setRegionOrCountry(excel.getInstanceFamilyToZoneNameRate());

        List<String> instanceFamilyToDeviceTypeRateExcelHeader = excel.getInstanceFamilyToDeviceTypeRateExcelHeader();
        LongtermPredictTaskDO task = self.getTask(excel.getTaskId());

        LocalDate predictStart = task.getPredictStart();
        String dateStart = instanceFamilyToDeviceTypeRateExcelHeader.get(4);
        // 将 predictStart 转换为 Excel 格式的日期字符串
        String formattedPredictStart = formatDate(predictStart);
        String msg = null;
        // 2024年11月, 和 predictStart 不同报错
        if (dateStart == null || !formattedPredictStart.equals(dateStart.trim())) {
            msg = String.format("开始日期错误,Excel第5列表头应该为: %s, 实际为: %s", formattedPredictStart, dateStart);
        }
        ConfigSplitRateDiffResp configSplitRateDiffResp = getConfigSplitRateDiffResp(web, excel);
        if (!Strings.isBlank(msg)) {
            configSplitRateDiffResp.getInstanceFamilyToDeviceTypeRateWarning().add(msg);
        }
        return configSplitRateDiffResp;
    }

    private static String formatDate(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        return date.format(formatter);
    }

    private ConfigSplitRateDiffResp getConfigSplitRateDiffResp(
            ConfigSplitRateDiffReq old, ConfigSplitRateDiffReq newOne) {

        List<String> zoneNames = longtermPredictDictService.getCampusToZoneInfo().values().stream()
                .map(ZoneRegionInfoDTO::getZoneName).collect(Collectors.toList());

        ConfigSplitRateDiffResp resp = new ConfigSplitRateDiffResp();

        List<CustomhouseTitleRateDiff> customhouseTitleRateDiff = new ArrayList<>();
        resp.setCustomhouseTitleRateDiff(customhouseTitleRateDiff);
        ListUtils2.groupAndApply(
                old.getCustomhouseTitleRate(), CustomhouseTitleRate::getCustomhouseTitle,
                newOne.getCustomhouseTitleRate(), CustomhouseTitleRate::getCustomhouseTitle,
                (key1, list1web, list2excel) -> {
                    List<CustomhouseTitleRateDiff> diff = getDiff(CustomhouseTitleRateDiff.class, list1web, list2excel);
                    customhouseTitleRateDiff.addAll(diff);
                });
        EStream.of(customhouseTitleRateDiff).groupAndConsume((o) -> "TOTAL", (key, list) -> {
            if (existNotDel(list) && isEqualOne(getSum(list))) {
                resp.getCustomhouseTitleRateWarning().add(String.format("维度[%s]下的比例和不为1", key));
            }
        });

        List<BizRangeTypeRateDiff> bizRangeTypeRateDiff = new ArrayList<>();
        resp.setBizRangeTypeRateDiff(bizRangeTypeRateDiff);
        ListUtils2.groupAndApply(
                old.getBizRangeTypeRate(), BizRangeTypeRate::getBizRangeType,
                newOne.getBizRangeTypeRate(), BizRangeTypeRate::getBizRangeType,
                (key1, list1web, list2excel) -> {
                    List<BizRangeTypeRateDiff> diff = getDiff(BizRangeTypeRateDiff.class, list1web, list2excel);
                    bizRangeTypeRateDiff.addAll(diff);
                });
        EStream.of(bizRangeTypeRateDiff).groupAndConsume((o) -> "TOTAL", (key, list) -> {
            if (existNotDel(list) && isEqualOne(getSum(list))) {
                resp.getBizRangeTypeRateWarning().add(String.format("维度[%s]下的比例和不为1", key));
            }
        });

        List<InstanceFamilyRateDiff> instanceFamilyRateDiff = new ArrayList<>();
        resp.setInstanceFamilyRateDiff(instanceFamilyRateDiff);
        ListUtils2.groupAndApply(
                old.getInstanceFamilyRate(), SplitInstanceFamilyKeyGetter::splitInstanceFamilyKey,
                newOne.getInstanceFamilyRate(), SplitInstanceFamilyKeyGetter::splitInstanceFamilyKey,
                (key1, list1web, list2excel) -> {
                    List<InstanceFamilyRateDiff> diff = getDiff(InstanceFamilyRateDiff.class, list1web, list2excel);
                    instanceFamilyRateDiff.addAll(diff);
                });
        EStream.of(instanceFamilyRateDiff)
                .groupAndConsume(SplitInstanceFamilyKeyGetter::splitInstanceFamilyPrefixKey,
                        (key, list) -> {
                            if (existNotDel(list) && isEqualOne(getSum(list))) {
                                resp.getInstanceFamilyRateWarning().add(String.format("维度[%s]下的比例和不为1", key));
                            }
                            EStream.of(list).forEach((o) -> {
                                String instanceFamily = o.getInstanceFamily();
                                if (!Lang.list("高IO型", "标准型", "大数据型", "内存型", "计算型")
                                        .contains(instanceFamily)) {
                                    String info = String.format("存在未知机型大类: %s", o.getInstanceFamily());
                                    resp.getInstanceFamilyRateWarning().add(info);
                                }
                            });
                        });

        genDeviceTypeDiff(old, newOne, resp);

        List<InstanceFamilyToZoneNameRateDiff> instanceFamilyToZoneNameRateDiff = new ArrayList<>();
        resp.setInstanceFamilyToZoneNameRateDiff(instanceFamilyToZoneNameRateDiff);
        ListUtils2.groupAndApply(
                old.getInstanceFamilyToZoneNameRate(), SplitZoneNameKeyGetter::splitZoneNameKey,
                newOne.getInstanceFamilyToZoneNameRate(), SplitZoneNameKeyGetter::splitZoneNameKey,
                (key1, list1web, list2excel) -> {
                    List<InstanceFamilyToZoneNameRateDiff> diff =
                            getDiff(InstanceFamilyToZoneNameRateDiff.class, list1web, list2excel);
                    instanceFamilyToZoneNameRateDiff.addAll(diff);
                    EStream.of(list2excel).forEach((o) -> {
                        String instanceFamily = o.getInstanceFamily();
                        if (!Lang.list("高IO型", "标准型", "大数据型", "内存型", "计算型")
                                .contains(instanceFamily)) {
                            String info = String.format("存在未知机型大类: %s", o.getInstanceFamily());
                            resp.getInstanceFamilyToZoneNameRateWarning().add(info);
                        }
                        if (!zoneNames.contains(o.getZoneName())) {
                            String info = String.format("存在未知可用区: %s", o.getZoneName());
                            resp.getInstanceFamilyToZoneNameRateWarning().add(info);
                        }
                    });
                });
        EStream.of(instanceFamilyToZoneNameRateDiff)
                .groupAndConsume(SplitZoneNameKeyGetter::splitZoneNamePrefixKey,
                        (key, list) -> {
                            if (existNotDel(list) && isEqualOne(getSum(list))) {
                                resp.getInstanceFamilyToDeviceTypeRateWarning()
                                        .add(String.format("维度[%s]下的比例和不为1", key));
                            }
                        });

        List<MonthRateDiff> monthRateDiff = new ArrayList<>();
        resp.setMonthRateDiff(monthRateDiff);
        ListUtils2.groupAndApply(
                old.getMonthRate(), SplitMonthKeyGetter::splitMonthKey,
                newOne.getMonthRate(), SplitMonthKeyGetter::splitMonthKey,
                (key1, list1web, list2excel) -> {
                    List<MonthRateDiff> diff = getDiff(MonthRateDiff.class, list1web, list2excel);
                    monthRateDiff.addAll(diff);
                });
        EStream.of(monthRateDiff)
                .groupAndConsume(SplitMonthKeyGetter::splitMonthPrefixKey,
                        (key, list) -> {
                            if (existNotDel(list) && isEqualOne(getSum(list))) {
                                resp.getInstanceFamilyToDeviceTypeRateWarning()
                                        .add(String.format("维度[%s]下的比例和不为1", key));
                            }
                        });
        return resp;
    }

    private void genDeviceTypeDiff(ConfigSplitRateDiffReq old, ConfigSplitRateDiffReq newOne,
            ConfigSplitRateDiffResp resp) {

        Map<String, String> deviceTypeToInstanceTypeMap = longtermPredictDictService.getDeviceTypeToInstanceTypeMap();
        LongtermPredictTaskDO task = self.getTask(old.getTaskId());

        List<InstanceFamilyToDeviceTypeRateDiff> instanceFamilyToDeviceTypeRateDiff = new ArrayList<>();
        resp.setInstanceFamilyToDeviceTypeRateDiff(instanceFamilyToDeviceTypeRateDiff);
        ListUtils2.groupAndApply(
                old.getInstanceFamilyToDeviceTypeRate(), SplitDeviceTypeKeyGetter::splitDeviceTypeKey,
                newOne.getInstanceFamilyToDeviceTypeRate(), SplitDeviceTypeKeyGetter::splitDeviceTypeKey,
                (key1, list1web, list2excel) -> {
                    boolean contains = ListUtils.contains(list2excel, (o) -> o.getRate() == null);
                    if (contains) {
                        String info = String.format("%s 维度存在null的默认比例", key1);
                        resp.getInstanceFamilyToDeviceTypeRateWarning().add(info);
                        return;
                    }
                    List<InstanceFamilyToDeviceTypeRateDiff> diff = getDiff(list1web, list2excel);
                    instanceFamilyToDeviceTypeRateDiff.addAll(diff);
                    EStream.of(list2excel).forEach((o) -> {
                        String instanceFamily = o.getInstanceFamily();
                        if (!Lang.list("高IO型", "标准型", "大数据型", "内存型", "计算型")
                                .contains(instanceFamily)) {
                            String info = String.format("存在未知机型大类: %s", o.getInstanceFamily());
                            resp.getInstanceFamilyToDeviceTypeRateWarning().add(info);
                        }
                        String deviceType = o.getDeviceType();
                        if (!deviceTypeToInstanceTypeMap.containsKey(deviceType)) {
                            String info = String.format("存在未知物理机机型: %s", deviceType);
                            resp.getInstanceFamilyToDeviceTypeRateWarning().add(info);
                        }
                    });
                });

        ListUtils2.groupThenAccept(
                newOne.getInstanceFamilyToDeviceTypeRate(),
                SplitDeviceTypeKeyGetter::splitDeviceTypePrefixKey,
                (key1, list) -> {
                    Map<String, BigDecimal> ymMap = new HashMap<>();
                    for (InstanceFamilyToDeviceTypeRate a : list) {
                        if (a.getYearMonthItemList() == null) {
                            continue;
                        }
                        for (YearMonthItem b : a.getYearMonthItemList()) {
                            String key = b.getYm() + a.getInstanceFamily();
                            if (b.getRate() != null) {
                                if (!ymMap.containsKey(key)) {
                                    ymMap.put(key, BigDecimal.ZERO);
                                }
                                ymMap.put(key, ymMap.get(key).add(b.getRate()));
                            } else {
                                if (ymMap.containsKey(key)) {
                                    String format = "拆分物理机的月特定比例中,维度:%s %s月份存在 null值";
                                    String info = String.format(format, key1, b.getYm().toString());
                                    resp.getInstanceFamilyToDeviceTypeRateWarning().add(info);
                                }
                            }
                        }
                    }
                    ymMap.forEach((k, v) -> {
                        boolean isOne = v.subtract(BigDecimal.ONE).abs().compareTo(BigDecimal.valueOf(0.01)) < 0;
                        if (!isOne) {
                            String format = "拆分物理机的月特定比例中,维度:%s-%s月份的比例不为 100%%,请检查配置";
                            String info = String.format(format, key1, k);
                            resp.getInstanceFamilyToDeviceTypeRateWarning().add(info);
                        }
                    });
                });

        EStream.of(instanceFamilyToDeviceTypeRateDiff)
                .groupAndConsume(SplitDeviceTypeKeyGetter::splitDeviceTypePrefixKey,
                        (key, list) -> {
                            if (existNotDel(list) && isEqualOne(getSum(list))) {
                                resp.getInstanceFamilyToDeviceTypeRateWarning()
                                        .add(String.format("维度[%s]下的比例和不为1", key));
                            }
                        });
    }


    private static void throwIfInstanceFamilyError(String instanceFamily, String info) {
        if (!Lang.list("高IO型", "标准型", "大数据型", "内存型", "计算型").contains(instanceFamily)) {
            if (Strings.isNotBlank(instanceFamily)) {
                throw BizException.makeThrow(info);
            }
            throw BizException.makeThrow("%s: 实例大类异常,未知实例大类");
        }
    }

    @Override
    public ConfigSplitRateDiffResp diffDbConfigSplitRate(ConfigSplitRateDiffReq web) {
        ConfigSplitRateResp dbOrigin = this.queryConfigSplitRate(web.getTaskId());
        ConfigSplitRateDiffReq db = getConfigSplitRateDiffReq(dbOrigin);
        db.setTaskId(web.getTaskId());
        return getConfigSplitRateDiffResp(db, web);
    }

    private static ConfigSplitRateDiffReq getConfigSplitRateDiffReq(ConfigSplitRateResp dbOrigin) {
        ConfigSplitRateDiffReq db = new ConfigSplitRateDiffReq();

        EStream.of(dbOrigin.getCustomhouseTitleRate()).map(CustomhouseTitleRate::from)
                .consumeAsList(db::setCustomhouseTitleRate);
        EStream.of(dbOrigin.getBizRangeTypeRate()).map(BizRangeTypeRate::from)
                .consumeAsList(db::setBizRangeTypeRate);

        EStream.of(dbOrigin.getInstanceFamilyRate()).map((o) -> {
            InstanceFamilyRate one = new InstanceFamilyRate();
            BeanUtils.copyProperties(o, one);
            return one;
        }).consumeAsList(db::setInstanceFamilyRate);
        EStream.of(dbOrigin.getInstanceFamilyToDeviceTypeRate()).map((o) -> {
            InstanceFamilyToDeviceTypeRate one = new InstanceFamilyToDeviceTypeRate();
            BeanUtils.copyProperties(o, one);
            return one;
        }).consumeAsList(db::setInstanceFamilyToDeviceTypeRate);
        EStream.of(dbOrigin.getInstanceFamilyToZoneNameRate()).map((o) -> {
            InstanceFamilyToZoneNameRate one = new InstanceFamilyToZoneNameRate();
            BeanUtils.copyProperties(o, one);
            return one;
        }).consumeAsList(db::setInstanceFamilyToZoneNameRate);
        EStream.of(dbOrigin.getMonthRate()).map((o) -> {
            MonthRate one = new MonthRate();
            BeanUtils.copyProperties(o, one);
            return one;
        }).consumeAsList(db::setMonthRate);
        return db;
    }


    @Override
    @Transactional(value = "cdlabTransactionManager")
    @Synchronized(customExceptionMessage = "配置正在覆盖中，请重试。", waitLockMillisecond = 10)
    public ConfigSplitRateDiffResp saveConfigSplitRate(ConfigSplitRateDiffReq req) {
        ConfigSplitRateResp db = queryConfigSplitRate(req.getTaskId());

        cdLabDbHelper.delete(LongtermPredictConfigCumstomhouseTitleRateDO.class, "where 1=1");
        cdLabDbHelper.delete(LongtermPredictConfigBizRangeTypeRateDO.class, "where 1=1");
        cdLabDbHelper.delete(LongtermPredictConfigInstanceFamilyRateDO.class, "where 1=1");
        cdLabDbHelper.delete(LongtermPredictConfigInstanceFamilyToDeviceTypeRateDO.class, "where 1=1");
        cdLabDbHelper.delete(LongtermPredictConfigInstanceFamilyToZoneNameRateDO.class, "where 1=1");
        cdLabDbHelper.delete(LongtermPredictConfigMonthRateDO.class, "where 1=1");

        setRegionOrCountry(req.getInstanceFamilyToZoneNameRate());
        cdLabDbHelper.insertBatchWithoutReturnId(EStream.of(req.getCustomhouseTitleRate())
                .map(CustomhouseTitleRate::toDo).toList());
        cdLabDbHelper.insertBatchWithoutReturnId(EStream.of(req.getBizRangeTypeRate())
                .map(BizRangeTypeRate::toDo).toList());
        cdLabDbHelper.insertBatchWithoutReturnId(EStream.of(req.getInstanceFamilyRate())
                .map(InstanceFamilyRate::toDo).toList());
        cdLabDbHelper.insertBatchWithoutReturnId(EStream.of(req.getInstanceFamilyToDeviceTypeRate())
                .map(InstanceFamilyToDeviceTypeRate::toDo).toList());
        cdLabDbHelper.insertBatchWithoutReturnId(EStream.of(req.getInstanceFamilyToZoneNameRate())
                .map(InstanceFamilyToZoneNameRate::toDo).toList());
        cdLabDbHelper.insertBatchWithoutReturnId(EStream.of(req.getMonthRate()).map(MonthRate::toDo).toList());

        ConfigSplitRateDiffReq dbOrigin = getConfigSplitRateDiffReq(db);
        dbOrigin.setTaskId(req.getTaskId());
        return getConfigSplitRateDiffResp(dbOrigin, req);
    }

    public interface RegionOrCountrySetter {

        void setRegionOrCountry(String regionOrCountry);

        String getZoneName();
    }

    @Override
    public Map<String, String> zoneNameToRegionOrCountry() {
        Map<String, String> zoneName2RegionName = longtermPredictDictService.getZoneName2RegionName();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = longtermPredictDictService.getRegionNameInfoMap();
        Map<String, String> ret = new HashMap<>();
        for (Entry<String, String> kv : zoneName2RegionName.entrySet()) {
            SoeRegionNameCountryDO tmp = regionNameInfoMap.get(kv.getValue());
            if (tmp == null) {
                throw BizException.makeThrow("可用区映射国家配置缺失，请配置之后使用:%s,%s ", kv.getKey(),
                        kv.getValue());
            }
            String regionOrCountry =
                    !Strings.equals("境内", tmp.getCustomhouseTitle()) ? tmp.getCountryName() : tmp.getRegionName();
            ret.put(kv.getKey(), regionOrCountry);
        }
        return ret;
    }

    private void setRegionOrCountry(List<? extends RegionOrCountrySetter> instanceFamilyToZoneNameRate) {
        Map<String, String> zoneName2RegionName = longtermPredictDictService.getZoneName2RegionName();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = longtermPredictDictService.getRegionNameInfoMap();

        // 设置regionOrCountry
        instanceFamilyToZoneNameRate.forEach((o) -> {
            String zoneName = o.getZoneName();
            String regionName = zoneName2RegionName.get(zoneName);
            SoeRegionNameCountryDO soeRegionNameCountryDO = regionNameInfoMap.get(regionName);
            if (soeRegionNameCountryDO != null) {
                String countryName = soeRegionNameCountryDO.getCountryName();
                String customhouseTitle = soeRegionNameCountryDO.getCustomhouseTitle();
                if (Strings.equals(customhouseTitle, "境内")) {
                    o.setRegionOrCountry(regionName);
                } else {
                    o.setRegionOrCountry(countryName);
                }
            }
        });
    }

    @Override
    @SneakyThrows
    public ParseBigCustomInfoExcelResp parseBigCustomInfoExcel(MultipartFile excelFile, Long taskId) {

        LongtermPredictTaskDO task = dbQuery.getTask(taskId, true);
        List<BigCustomerExcelDTO> excelDataList = parseExcel(excelFile, task);

        ParseBigCustomInfoExcelResp resp = new ParseBigCustomInfoExcelResp();

        Map<String, String> deviceTypeToInstanceTypeMap = longtermPredictDictService.getDeviceTypeToInstanceTypeMap();
        Map<String, String> zoneNameToCampus = longtermPredictDictService.getZoneNameToCampus(taskId);

        List<String> warnings = new ArrayList<>();
        for (int i = 0; i < excelDataList.size(); i++) {
            BigCustomerExcelDTO one = excelDataList.get(i);
            if (Strings.isBlank(deviceTypeToInstanceTypeMap.get(one.getDeviceType()))) {
                warnings.add(String.format("第 %d 行，物理机: %s，没有找到对应的实例类型,请检查。", i + 2,
                        one.getDeviceType()));
            }
            if (Strings.isBlank(zoneNameToCampus.get(one.getZoneName()))) {
                warnings.add(
                        String.format("第 %d 行，可用区: %s，没有找到对应的campus,请检查。", i + 2, one.getZoneName()));
            }
        }
        resp.setWarning(warnings);
        resp.setYearMonth(YearMonth.from(task.getPredictStart()).toString());
        resp.setResult(excelDataList);
        return resp;
    }

    private static List<BigCustomerExcelDTO> parseExcel(MultipartFile excelFile,
            LongtermPredictTaskDO task) throws IOException {
        String sheetName = "大客户采购明细";
        List<String> excelHead = getBigCustomerExcelHeader(task.getPredictStart())
                .stream().map((o) -> o.get(0)).collect(Collectors.toList());
        List<BigCustomerExcelDTO> excelDataList = new ArrayList<>();
        List<ReadSheet> allSheets = EasyExcel.read(excelFile.getInputStream()).build().excelExecutor().sheetList();
        boolean sheetExists = allSheets.stream().anyMatch(sheet -> sheet.getSheetName().equals(sheetName));
        if (!sheetExists) {
            throw BizException.makeThrow("Excel缺少%s的工作表,请检查.", sheetName);
        }
        EasyExcel.read(excelFile.getInputStream(), BigCustomerExcelDTO.class,
                new AnalysisEventListener<BigCustomerExcelDTO>() {
                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        List<String> parseLine = IntStream.range(0, headMap.size())
                                .mapToObj(headMap::get).collect(Collectors.toList());
                        for (int i = 0; i < parseLine.size(); i++) {
                            if (!Strings.equals(parseLine.get(i), excelHead.get(i))) {
                                throw BizException.makeThrow("Excel 第%s列导入的表头不是导出的表头(导入表头: %s, 导出表头应该: %s),"
                                                + "请先导出然后用导出的EXCEL填写后导入数据.",
                                        convertToExcelColumn(i), parseLine.get(i), excelHead.get(i)
                                );
                            }
                        }
                    }

                    @Override
                    public void invoke(BigCustomerExcelDTO data, AnalysisContext context) {
                        excelDataList.add(data);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("解析完毕");
                    }
                }).sheet(sheetName).doRead();
        return excelDataList;
    }

    @SuppressWarnings("Convert2MethodRef")
    @Override
    public ParseBigCustomInfoExcelResp queryBigCustomInfo(TaskIdAndSplitIdReq req) {
        List<LongtermPredictInputBigCustomerPurchaseDO> all = cdLabDbHelper.getAll(
                LongtermPredictInputBigCustomerPurchaseDO.class,
                "where split_version_id=? ", req.getSplitVersionId());

        LongtermPredictTaskDO task = dbQuery.getTask(req.getTaskId(), true);
        LocalDate predictStart = task.getPredictStart();
        ParseBigCustomInfoExcelResp resp = new ParseBigCustomInfoExcelResp();
        List<BigCustomerExcelDTO> result = new ArrayList<>();
        resp.setResult(result);
        resp.setYearMonth(YearMonth.from(predictStart).toString());
        EStream.of(all)
                .groupAndConsume((o) -> Strings.join("@", o.getCustomerName(), o.getDeviceType(), o.getZoneName()),
                        (key, list) -> {
                            BigCustomerExcelDTO bigCustomerExcelDTO = BigCustomerExcelDTO.toDTO(list, predictStart);
                            result.add(bigCustomerExcelDTO);
                        });
        return resp;
    }

    @Override
    public CreateSplitVersionReq queryCreateSplitVersionReq(Long splitVersionId) {
        ErrorUtil.throwIfNull(splitVersionId, "splitVersionId 为空");
        LongtermPredictOutputSplitVersionDO versionDO
                = cdLabDbHelper.getByKey(LongtermPredictOutputSplitVersionDO.class, splitVersionId);
        if (versionDO == null || versionDO.getSplitArg() == null) {
            return new CreateSplitVersionReq();
        }
        return JSON.parse(versionDO.getSplitArg(), CreateSplitVersionReq.class);
    }

    @Override
    public ImmutableMap<String, Object> deleteSplitVersionById(Long splitVersionId) {
        int delete = cdLabDbHelper.delete(LongtermPredictOutputSplitVersionDO.class, "where id=?", splitVersionId);
        return ImmutableMap.of("num", delete);
    }

    @Override
    public DownloadBean downloadBigCustomInfoExcel(DownLoadExcelReq req) {

        LongtermPredictTaskDO task = dbQuery.getTask(req.getTaskId(), true);
        LocalDate predictStart = task.getPredictStart();

        Long versionId = req.getSplitVersionId();
        List<LongtermPredictInputBigCustomerPurchaseDO> all = cdLabDbHelper.getAll(
                LongtermPredictInputBigCustomerPurchaseDO.class, "where split_version_id=?", versionId);

        List<BigCustomerExcelDTO> excelData = new ArrayList<>();
        Function<LongtermPredictInputBigCustomerPurchaseDO, String> keyFunc =
                (o) -> Strings.join("@", o.getCustomerName(), o.getDeviceType(), o.getZoneName());
        EStream.of(all).groupAndConsume(keyFunc, (key, list) -> {
            BigCustomerExcelDTO bigCustomerExcelDTO = BigCustomerExcelDTO.toDTO(list, predictStart);
            excelData.add(bigCustomerExcelDTO);
        });
        List<List<String>> excelHead = getBigCustomerExcelHeader(predictStart);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        com.alibaba.excel.ExcelWriter excelWriter = EasyExcel.write(out)
                .registerWriteHandler(new DownloadExcelStyleHandler()).build();
        WriteSheet writeSheet0 = EasyExcel.writerSheet(0, "大客户采购明细").head(excelHead).build();
        excelWriter.write(excelData, writeSheet0).finish();
        return new DownloadBean("大客户预测数据.xlsx", out.toByteArray());
    }

    private static List<List<String>> getBigCustomerExcelHeader(LocalDate predictStart) {
        List<List<String>> excelHead = Lang.list(
                Lang.list("客户名称"), Lang.list("物理机机型"), Lang.list("可用区")
        );
        for (int i = 0; i < 24; i++) {
            YearMonth c = YearMonth.from(predictStart).plusMonths(i);
            excelHead.add(Lang.list(c.toString()));
        }
        return excelHead;
    }


    private static boolean isEqualOne(BigDecimal sum) {
        return sum.subtract(BigDecimal.ONE).abs().compareTo(BigDecimal.valueOf(0.01)) >= 0;
    }

    private static boolean existNotDel(List<? extends DiffInfoSetter> list) {
        return EStream.of(list).anyMatch(i -> !Strings.equals("DELETE", i.getDiffInfo().getType()));
    }

    private static BigDecimal getSum(List<? extends DiffInfoSetter> list) {
        return EStream.of(list)
                .filter((o) -> !Strings.equals("DELETE", o.getDiffInfo().getType()))
                .map((o) -> {
                    if (o.getDiffInfo().getRateAfterChange() != null) {
                        return o.getDiffInfo().getRateAfterChange()
                                .setScale(2, RoundingMode.HALF_UP);
                    }
                    return BigDecimal.ZERO;
                })
                .sum((o) -> o);
    }

    private List<InstanceFamilyToDeviceTypeRateDiff> getDiff(
            List<InstanceFamilyToDeviceTypeRate> list1web,
            List<InstanceFamilyToDeviceTypeRate> list2excel) {
        String dupInfo = "重复数据，默认使用第一条，这条删除";
        String coverInfo = "覆盖删除";
        Class<InstanceFamilyToDeviceTypeRateDiff> clz = InstanceFamilyToDeviceTypeRateDiff.class;
        List<InstanceFamilyToDeviceTypeRateDiff> rateDiff = new ArrayList<>();
        if (list1web == null) {
            rateDiff.add(DiffInfoSetter.getInsertType(clz, list2excel.get(0)));
            EStream.of(list2excel).skip(1).map(o -> DiffInfoSetter.getDeleteType(clz, o, dupInfo))
                    .consumeAsList(rateDiff::addAll);
        } else if (list2excel == null) {
            EStream.of(list1web).map((o) -> DiffInfoSetter.getDeleteType(clz, o, coverInfo))
                    .consumeAsList(rateDiff::addAll);
        } else {
            InstanceFamilyToDeviceTypeRateDiff t;
            // 如果差异值小于0.01,就认为没有差异
            if (list2excel.get(0).getRate().subtract(list1web.get(0).getRate()).abs()
                    .compareTo(BigDecimal.valueOf(0.01)) < 0) {
                t = DiffInfoSetter.getEqualType(clz, list2excel.get(0), list1web.get(0));
            } else {
                t = DiffInfoSetter.getUpdateType(clz, list2excel.get(0), list1web.get(0));
            }
            rateDiff.add(t);
            EStream.of(list2excel).skip(1).map(o -> DiffInfoSetter.getDeleteType(clz, o, dupInfo))
                    .consumeAsList(rateDiff::addAll);
            EStream.of(list1web).skip(1).map(o -> DiffInfoSetter.getDeleteType(clz, o, dupInfo))
                    .consumeAsList(rateDiff::addAll);

            //todo 判断默认rate 一定不可以为空
            List<YearMonthItemDiff> yearMonthItemsDiffInfo = new ArrayList<>();
            t.setYearMonthItemsDiffInfo(yearMonthItemsDiffInfo);
            ListUtils2.groupAndApply(
                    list1web.get(0).getYearMonthItemList(), YearMonthItem::getYm,
                    list2excel.get(0).getYearMonthItemList(), YearMonthItem::getYm,
                    (ym, list1, list2) -> {
                        Class<YearMonthItemDiff> ymClz = YearMonthItemDiff.class;
                        YearMonthItemDiff diff = null;
                        if (list1 == null) {
                            diff = DiffInfoSetter.getInsertType(ymClz, list2.get(0));
                        } else if (list2 == null) {
                            diff = DiffInfoSetter.getDeleteType(ymClz, list1.get(0), "覆盖删除");
                        } else {
                            if (list1.get(0).getRate() != null && list2.get(0).getRate() != null) {
                                if (list1.get(0).getRate().subtract(list2.get(0).getRate()).abs()
                                        .compareTo(BigDecimal.valueOf(0.01)) < 0) {
                                    diff = DiffInfoSetter.getEqualType(ymClz, list2.get(0), list1.get(0));
                                } else {
                                    diff = DiffInfoSetter.getUpdateType(ymClz, list2.get(0), list1.get(0));
                                }
                            } else if (list1.get(0).getRate() == null && list2.get(0).getRate() != null) {
                                diff = DiffInfoSetter.getInsertType(ymClz, list2.get(0));
                            } else if (list1.get(0).getRate() != null && list2.get(0).getRate() == null) {
                                diff = DiffInfoSetter.getDeleteType(ymClz, list1.get(0), "覆盖删除");
                            } else if (list1.get(0).getRate() == null && list2.get(0).getRate() == null) {
                                diff = DiffInfoSetter.getEqualType(ymClz, list2.get(0), list1.get(0));
                            }
                        }
                        yearMonthItemsDiffInfo.add(diff);
                    });
        }
        return rateDiff;
    }

    private <T extends DiffInfoSetter> List<T> getDiff(Class<T> clz,
            List<? extends RateGetter> list1web, List<? extends RateGetter> list2excel) {
        String dupInfo = "重复数据，默认使用第一条，这条删除";
        String coverInfo = "覆盖删除";
        List<T> rateDiff = new ArrayList<>();
        if (list1web == null) {
            rateDiff.add(DiffInfoSetter.getInsertType(clz, list2excel.get(0)));
            EStream.of(list2excel).skip(1).map(o -> DiffInfoSetter.getDeleteType(clz, o, dupInfo))
                    .consumeAsList(rateDiff::addAll);
        } else if (list2excel == null) {
            EStream.of(list1web).map((o) -> DiffInfoSetter.getDeleteType(clz, o, coverInfo))
                    .consumeAsList(rateDiff::addAll);
        } else {
            T t;
            // 如果差异值小于0.01,就认为没有差异
            //todo 这里用户可能没有填写,要转换成空的
            if (list2excel.get(0).getRate().subtract(list1web.get(0).getRate()).abs()
                    .compareTo(BigDecimal.valueOf(0.01)) < 0) {
                t = DiffInfoSetter.getEqualType(clz, list2excel.get(0), list1web.get(0));
            } else {
                t = DiffInfoSetter.getUpdateType(clz, list2excel.get(0), list1web.get(0));
            }
            rateDiff.add(t);
            EStream.of(list2excel).skip(1).map(o -> DiffInfoSetter.getDeleteType(clz, o, dupInfo))
                    .consumeAsList(rateDiff::addAll);
            EStream.of(list1web).skip(1).map(o -> DiffInfoSetter.getDeleteType(clz, o, dupInfo))
                    .consumeAsList(rateDiff::addAll);

            if (clz == InstanceFamilyToDeviceTypeRateDiff.class) {

                BigDecimal rate = list2excel.get(0).getRate();

            }


        }
        return rateDiff;
    }


    private static void addWarningInfo(List<InstanceToDeviceItemDiff> diffData, InstanceToDeviceResp resp) {
        EStream.of(diffData).groupAndConsume((o) -> o.getKey().getPrefixKey(), (key, list) -> {
            BigDecimal sum = EStream.of(list)
                    .filter((o) -> !Strings.equals("DELETE", o.getType()))
                    .map((o) -> o.getRateAfterChange().setScale(2, RoundingMode.HALF_UP))
                    .sum((o) -> o);
            boolean hasNotDel = EStream.of(list).anyMatch(i -> !Strings.equals("DELETE", i.getType()));
            if (hasNotDel && sum.subtract(BigDecimal.ONE).abs().compareTo(BigDecimal.valueOf(0.01)) >= 0) {
                resp.getWarnings().add(String.format("维度[%s]下的比例和不为1", key));
            }
        });
    }

    private static List<InstanceToDeviceItemDiff> getDiffInfo(List<InstanceToDeviceItem> oldOne,
            List<InstanceToDeviceItem> newOne) {
        List<InstanceToDeviceItemDiff> diffData = new ArrayList<>();
        ListUtils2.groupAndApply(
                oldOne, InstanceToDeviceItem::getKey,
                newOne, InstanceToDeviceItem::getKey,
                (key1, list1web, list2excel) -> {
                    if (list1web == null) {
                        diffData.add(getInsertType(list2excel.get(0)));
                        deleteLeft(list2excel, "重复数据上传，默认使用第一条，这条删除", diffData);
                    } else if (list2excel == null) {
                        for (InstanceToDeviceItem instanceToDeviceItem : list1web) {
                            InstanceToDeviceItemDiff errorDiff = getDeleteType(instanceToDeviceItem, "覆盖删除");
                            diffData.add(errorDiff);
                        }
                    } else {
                        InstanceToDeviceItemDiff updateDiff = new InstanceToDeviceItemDiff();
                        InstanceToDeviceItemDiff.setSuper(updateDiff, list2excel.get(0));
                        updateDiff.setType("UPDATE");
                        updateDiff.setInfo("修改");
                        updateDiff.setRate(list1web.get(0).getRate());
                        updateDiff.setRateAfterChange(list2excel.get(0).getRate());
                        diffData.add(updateDiff);
                        deleteLeft(list2excel, "重复数据上传，默认使用第一条，这条删除", diffData);
                        deleteLeft(list1web, "重复数据，此条删除", diffData);
                    }
                });
        return diffData;
    }


    @SuppressWarnings("Convert2MethodRef")
    private static DateColumList getScaleColumList(List<LongtermPredictOutputScaleSplitDO> list) {
        DateColumList dateColumList = new DateColumList();
        EStream.of(list).map((o) -> o.getQuarterName()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setQuarterDateNotStrict(o));
        EStream.of(list).map((o) -> o.getHalfYearName()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setHalfYearDateNotStrict(o));
        EStream.of(list).map((o) -> o.getYearName()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setYearDateNotStrict(o));
        EStream.of(list).map((o) -> o.getYearMonthStr()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setMonthDate(o));
        EStream.of(list).groupBy((o) -> o.getQuarterName()).map((kv) -> {
            List<YearMonth> ym = EStream.of(kv.getValue()).map((o) -> o.getYearMonth()).distinct().sorted().toList();
            if (3 != ym.size()) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList((o) -> dateColumList.setQuarterDate(o));
        EStream.of(list).groupBy((o) -> o.getHalfYearName()).map((kv) -> {
            List<YearMonth> ym = EStream.of(kv.getValue()).map((o) -> o.getYearMonth()).distinct().sorted().toList();
            if (6 != ym.size()) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList((o) -> dateColumList.setHalfYearDate(o));
        EStream.of(list).groupBy((o) -> o.getYearName()).map((kv) -> {
            List<YearMonth> ym = EStream.of(kv.getValue()).map((o) -> o.getYearMonth()).distinct().sorted().toList();
            if (12 != ym.size()) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList((o) -> dateColumList.setYearDate(o));
        return dateColumList;
    }


    @SuppressWarnings("Convert2MethodRef")
    private static DateColumList getColumListWithIndustr(List<LongtermPredictOutputPurchaseSplitIndustryDeptDO> list) {
        DateColumList dateColumList = new DateColumList();
        EStream.of(list).map((o) -> o.getQuarterName()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setQuarterDateNotStrict(o));
        EStream.of(list).map((o) -> o.getHalfYearName()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setHalfYearDateNotStrict(o));
        EStream.of(list).map((o) -> o.getYearMonthStr()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setMonthDate(o));
        EStream.of(list).map((o) -> o.getYearName()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setYearDateNotStrict(o));
        EStream.of(list).groupBy((o) -> o.getQuarterName()).map((kv) -> {
            List<YearMonth> ym = EStream.of(kv.getValue()).map((o) -> o.getYearMonth()).distinct().sorted().toList();
            if (3 != ym.size()) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList((o) -> dateColumList.setQuarterDate(o));

        EStream.of(list).groupBy((o) -> o.getHalfYearName()).map((kv) -> {
            List<YearMonth> ym = EStream.of(kv.getValue()).map((o) -> o.getYearMonth()).distinct().sorted().toList();
            if (6 != ym.size()) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList((o) -> dateColumList.setHalfYearDate(o));
        EStream.of(list).groupBy((o) -> o.getYearName()).map((kv) -> {
            List<YearMonth> ym = EStream.of(kv.getValue()).map((o) -> o.getYearMonth()).distinct().sorted().toList();
            if (12 != ym.size()) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList((o) -> dateColumList.setYearDate(o));
        return dateColumList;
    }

    @SuppressWarnings("Convert2MethodRef")
    private static <T extends DateAccessor> DateColumList getColumList(List<T> list) {
        DateColumList dateColumList = new DateColumList();
        EStream.of(list).map((o) -> o.getQuarterName()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setQuarterDateNotStrict(o));
        EStream.of(list).map((o) -> o.getHalfYearName()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setHalfYearDateNotStrict(o));
        EStream.of(list).map((o) -> o.getYearName()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setYearDateNotStrict(o));

        EStream.of(list).groupBy((o) -> o.getQuarterName()).map((kv) -> {
            List<YearMonth> ym = EStream.of(kv.getValue()).map((o) -> o.getYearMonth()).distinct().sorted().toList();
            if (3 != ym.size()) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList((o) -> dateColumList.setQuarterDate(o));

        EStream.of(list).groupBy((o) -> o.getHalfYearName()).map((kv) -> {
            List<YearMonth> ym = EStream.of(kv.getValue()).map((o) -> o.getYearMonth()).distinct().sorted().toList();
            if (6 != ym.size()) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList((o) -> dateColumList.setHalfYearDate(o));

        EStream.of(list).groupBy((o) -> o.getYearName()).map((kv) -> {
            List<YearMonth> ym = EStream.of(kv.getValue()).map((o) -> o.getYearMonth()).distinct().sorted().toList();
            if (12 != ym.size()) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList((o) -> dateColumList.setYearDate(o));

        EStream.of(list).map((o) -> o.getYearMonthStr()).distinct().sorted()
                .consumeAsList((o) -> dateColumList.setMonthDate(o));
        return dateColumList;
    }


    @SuppressWarnings("Convert2MethodRef")
    private static Item1 getDateInfo1(
            List<LongtermPredictOutputScaleSplitDO> dateList, DateColumList dateColumList) {
        Item1 oneKey = Item1.from(dateList.get(0));

        Map<String, BigDecimal> quarterMap = EStream.of(dateList).groupBy((o) -> o.getQuarterName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getCurCore()));
        EStream.of(dateColumList.getQuarterDateNotStrict())
                .map((o) -> quarterMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setQuarterPurchaseCore(l));

        Map<String, BigDecimal> halfYearMap = EStream.of(dateList).groupBy((o) -> o.getHalfYearName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getCurCore()));
        EStream.of(dateColumList.getHalfYearDateNotStrict())
                .map((o) -> halfYearMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setHalfYearPurchaseCore(l));

        Map<String, BigDecimal> yearMonthStrMap = EStream.of(dateList).groupBy((o) -> o.getYearMonthStr())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getCurCore()));
        EStream.of(dateColumList.getMonthDate())
                .map((o) -> yearMonthStrMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setMonthPurchaseCore(l));
        return oneKey;
    }

    @SuppressWarnings("Convert2MethodRef")
    private static Item2 getDateInfo2(
            List<LongtermPredictOutputPurchaseSplitIndustryDeptDO> dateList, DateColumList dateColumList) {
        Item2 oneKey = Item2.from(dateList.get(0));

        Map<String, BigDecimal> quarterMap = EStream.of(dateList).groupBy((o) -> o.getQuarterName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getQuarterDateNotStrict())
                .map((o) -> quarterMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setQuarterPurchaseCore(l));

        Map<String, BigDecimal> halfYearMap = EStream.of(dateList).groupBy((o) -> o.getHalfYearName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getHalfYearDateNotStrict())
                .map((o) -> halfYearMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setHalfYearPurchaseCore(l));

        Map<String, BigDecimal> yearMonthStrMap = EStream.of(dateList).groupBy((o) -> o.getYearMonthStr())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getMonthDate())
                .map((o) -> yearMonthStrMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setMonthPurchaseCore(l));
        return oneKey;
    }

    @SuppressWarnings("Convert2MethodRef")
    private static Item3 getDateInfo3(
            List<LongtermPredictOutputPurchaseSplitDO> dateList, DateColumList dateColumList) {
        Item3 oneKey = Item3.from(dateList.get(0));

        Map<String, BigDecimal> quarterMap = EStream.of(dateList).groupBy((o) -> o.getQuarterName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getQuarterDateNotStrict())
                .map((o) -> quarterMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setQuarterPurchaseCore(l));

        Map<String, BigDecimal> halfYearMap = EStream.of(dateList).groupBy((o) -> o.getHalfYearName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getHalfYearDateNotStrict())
                .map((o) -> halfYearMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setHalfYearPurchaseCore(l));

        Map<String, BigDecimal> yearMonthStrMap = EStream.of(dateList).groupBy((o) -> o.getYearMonthStr())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getMonthDate())
                .map((o) -> yearMonthStrMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setMonthPurchaseCore(l));
        Map<String, Long> yearMonthStrIdMap = EStream.of(dateList).groupBy((o) -> o.getYearMonthStr())
                .toMap((kv) -> kv.getKey(), (kv) -> kv.getValue().get(0).getAutoIncrId());
        EStream.of(dateColumList.getMonthDate())
                .map((o) -> yearMonthStrIdMap.get(o))
                .consumeAsList((l) -> oneKey.setUpdateId(l));
        return oneKey;
    }


    @SuppressWarnings("Convert2MethodRef")
    private static AdjustItem getDateInfo4(List<LongtermPredictOutputPurchaseSplitAdjustDO> dateList,
            DateColumList dateColumList) {
        AdjustItem oneKey = AdjustItem.from(dateList.get(0));

        Map<String, BigDecimal> quarterMap = EStream.of(dateList).groupBy((o) -> o.getQuarterName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getQuarterDateNotStrict())
                .map((o) -> quarterMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setQuarterPurchaseCore(l));

        Map<String, BigDecimal> halfYearMap = EStream.of(dateList).groupBy((o) -> o.getHalfYearName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getHalfYearDateNotStrict())
                .map((o) -> halfYearMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setHalfYearPurchaseCore(l));

        Map<String, BigDecimal> yearMap = EStream.of(dateList).groupBy((o) -> o.getYearName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getYearDateNotStrict())
                .map((o) -> yearMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setYearPurchaseCore(l));

        Map<String, BigDecimal> yearMonthStrMap = EStream.of(dateList).groupBy((o) -> o.getYearMonthStr())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getMonthDate())
                .map((o) -> yearMonthStrMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setMonthPurchaseCore(l));
        return oneKey;
    }

    @SuppressWarnings("Convert2MethodRef")
    private static Item getDateInfo(List<LongtermPredictOutputPurchaseSplitDO> dateList, DateColumList dateColumList) {
        Item oneKey = Item.from(dateList.get(0));

        Map<String, BigDecimal> quarterMap = EStream.of(dateList).groupBy((o) -> o.getQuarterName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getQuarterDateNotStrict())
                .map((o) -> quarterMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setQuarterPurchaseCore(l));

        Map<String, BigDecimal> halfYearMap = EStream.of(dateList).groupBy((o) -> o.getHalfYearName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getHalfYearDateNotStrict())
                .map((o) -> halfYearMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setHalfYearPurchaseCore(l));

        Map<String, BigDecimal> yearMap = EStream.of(dateList).groupBy((o) -> o.getYearName())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getYearDateNotStrict())
                .map((o) -> yearMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setYearPurchaseCore(l));

        Map<String, BigDecimal> yearMonthStrMap = EStream.of(dateList).groupBy((o) -> o.getYearMonthStr())
                .toMap((kv) -> kv.getKey(), (kv) -> EStream.of(kv.getValue()).sum((o) -> o.getPurchaseCore()));
        EStream.of(dateColumList.getMonthDate())
                .map((o) -> yearMonthStrMap.getOrDefault(o, BigDecimal.ZERO).setScale(0, RoundingMode.HALF_UP))
                .consumeAsList((l) -> oneKey.setMonthPurchaseCore(l));
        return oneKey;
    }

    private String generateUpdateSql(UpdateSplitRstReq req) {
        String tableName = "longterm_predict_output_purchase_split"; // 替换为你的表名
        String idColumn = "auto_incr_id"; // 替换为你的 ID 列名
        String valueColumn = "purchase_core"; // 替换为你的值列名

        String cases = req.getItems().stream()
                .map(item -> String.format("WHEN %d THEN %s", item.getUpdateId(), item.getPurchaseCore()))
                .collect(Collectors.joining(" "));

        String ids = req.getItems().stream()
                .map(item -> item.getUpdateId().toString())
                .collect(Collectors.joining(", "));

        return String.format("UPDATE %s SET %s = CASE %s %s END WHERE %s IN (%s);",
                tableName, valueColumn, idColumn, cases, idColumn, ids);
    }


    @SneakyThrows
    private ScaleSplitDependDTO getHistory(Long taskId) {

        CompletableFuture<List<LongtermPredictInputArgsDO>> inputFuture =
                CompletableFuture.supplyAsync(() -> self.getInputArgs(taskId));
        CompletableFuture<LongtermPredictTaskDO> taskFuture =
                CompletableFuture.supplyAsync(() -> self.getTask(taskId));
        CompletableFuture<List<LongtermPredictOutputScaleDO>> outputScale = CompletableFuture.supplyAsync(
                () -> LongtermPredictOutputScaleDO.db().getAll("where task_id=?", taskId));

        List<LongtermPredictInputArgsDO> allInputArgs = inputFuture.get();
        LongtermPredictTaskDO taskDO = taskFuture.get();
        List<LongtermPredictOutputScaleDO> totalOriginData = outputScale.get();
        return new ScaleSplitDependDTO(allInputArgs, taskDO, totalOriginData);
    }

    @Data
    public static class ScaleSplitDependDTO {

        public final LongtermPredictTaskDO taskDO;
        public final List<LongtermPredictInputArgsDO> allInputArgs;
        public final List<LongtermPredictOutputScaleDO> totalOriginData;

        // 这增加2个自定义的配置表
        public List<CustomhouseTitleRate> customhouseTitleRate;
        public List<BizRangeTypeRate> bizRangeTypeRate;

        public ScaleSplitDependDTO(List<LongtermPredictInputArgsDO> allInputArgs, LongtermPredictTaskDO taskDO,
                List<LongtermPredictOutputScaleDO> totalOriginData) {
            this.allInputArgs = allInputArgs;
            this.taskDO = taskDO;
            this.totalOriginData = totalOriginData;
        }
    }


    private void splitMonth(ScaleDataSplit tree, LongtermPredictOutputScaleDO originNeedSplitData) {

        LongtermPredictTaskDO taskDO = tree.getScaleSplitDependDTO().getTaskDO();
        LocalDate predictStart = taskDO.getPredictStart();
        LocalDate inputStartDate = originNeedSplitData.getStartStatTime();
        LongtermPredictInputArgsDO curInputArgs = tree.getCurInputArgs();
        LocalDate predictStartSub1M = predictStart.minusMonths(1);
        YearMonth latestHistory = YearMonth.of(predictStartSub1M.getYear(), predictStartSub1M.getMonthValue());
        // 默认递增值
        int predictLen = 6;
        BigDecimal stepVal = originNeedSplitData.getCurCore().subtract(originNeedSplitData.getStartCurCore())
                .divide(BigDecimal.valueOf(predictLen), 5, RoundingMode.HALF_UP);
        String msg = "平均拆分到每个月";
        if (predictStart.isAfter(inputStartDate)) { //todo范围不对
//            if (predictStart.withDayOfMonth(1).isAfter(inputStartDate.withDayOfMonth(1))) {
            int i = predictStart.getMonthValue() - inputStartDate.getMonthValue();
            predictLen = 6 - i;
            // 这里使用实际或者预测的数据来
            String strategyType = tree.getCurInputArgs().getStrategyType();
            List<LongtermPredictInputScaleDO> inputScaleDetail = getInputOrOutputScaleSub1M(taskDO, strategyType);
            if (inputScaleDetail.get(0).getId() == null) {
                msg += ",使用了预测的数据";
            }
            BigDecimal firstNum = null;
            if (taskDO.isDecisionCategory()) {
                firstNum = inputScaleDetail.stream()
                        .filter((o) -> Strings.equals(o.getYearMonthStr(), latestHistory.toString()))
                        .filter((o) -> {
                            if (Strings.isNotBlank(curInputArgs.getRegionName())) {
                                return Strings.equals(o.getRegionName(), curInputArgs.getRegionName());
                            } else if (Strings.isNotBlank(curInputArgs.getCountryName())) {
                                return Strings.equals(o.getCountryName(), curInputArgs.getCountryName());
                            } else {
                                throw BizException.makeThrow("regionName和RegionName 都是空的");
                            }
                        })
                        .map(LongtermPredictInputScaleDO::getCurCore)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                firstNum = inputScaleDetail.stream()
                        .filter((o) -> Strings.equals(o.getYearMonthStr(), latestHistory.toString()))
                        .map(LongtermPredictInputScaleDO::getCurCore).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            stepVal = originNeedSplitData.getCurCore().subtract(firstNum)
                    .divide(BigDecimal.valueOf(predictLen), 5, RoundingMode.HALF_UP);
        }

        // 目前只有一个来源，可以直接获取到开始的数据
        for (DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> node : tree.getLeafLevel()) {
            LongtermPredictOutputScaleSplitDO oneData = node.getData();
            // 从后往前，每往前一个月就减去一个 stepVal
            for (int i = 0; i < predictLen; i++) {
                LongtermPredictOutputScaleSplitDO oneSplit = new LongtermPredictOutputScaleSplitDO();
                BeanUtils.copyProperties(oneData, oneSplit);
                LocalDate originDate = oneSplit.getStatTime();
                YearMonth yearMonth = YearMonth.of(originDate.getYear(), originDate.getMonthValue());
                YearMonth afterSplitMonth = yearMonth.minusMonths(i);
                setDateInfo(oneSplit, afterSplitMonth);
                BigDecimal curCore = oneSplit.getCurCore();
                oneSplit.setCurCore(curCore.subtract(stepVal.multiply(BigDecimal.valueOf(i))));
                oneSplit.setStartCurCore(curCore.subtract(stepVal.multiply(BigDecimal.valueOf(i + 1))));
                if (taskDO.getPredictEnd().isBefore(oneSplit.getStatTime())) {
                    continue;
                }
                DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> treeNode =
                        DataSplitTreeNode.newNode(msg, oneSplit);
                tree.addNode(node, treeNode);
            }
        }
    }

    private List<LongtermPredictInputScaleDO> getInputOrOutputScaleSub6M(LongtermPredictTaskDO taskDO,
            String strategyType) {
        return getInputOrOutputScale(taskDO, strategyType, 6);
    }

    private List<LongtermPredictInputScaleDO> getInputOrOutputScaleSub1M(LongtermPredictTaskDO taskDO,
            String strategyType) {
        return getInputOrOutputScale(taskDO, strategyType, 1);
    }

    private List<LongtermPredictInputScaleDO> getInputOrOutputScale(LongtermPredictTaskDO taskDO,
            String strategyType, int subMonth) {
        LocalDate predictStartSub1M = taskDO.getPredictStart().minusMonths(subMonth);
        YearMonth latestHistory = YearMonth.of(predictStartSub1M.getYear(), predictStartSub1M.getMonthValue());

        List<LongtermPredictInputScaleDO> inputScaleDetail = self.getTaskScale(taskDO.getId(), latestHistory);
        // 如果拿到的数据是空的，尝试去longterm_predict_output_scale_split 中获取
        if (inputScaleDetail.isEmpty()) {
            // 从关联的taskId 中去获取到拆分预测的数据
            List<LongtermPredictOutputScaleSplitDO> scaleSplit =
                    self.getTaskScaleSplit(taskDO.getRelateTaskId(), latestHistory);
            scaleSplit = EStream.of(scaleSplit)
                    .filter((o -> Strings.equals(o.getStrategyType(), strategyType))).toList();
            inputScaleDetail = transFrom(scaleSplit);
        }
        // 可能数据还是没有， 再往前面找数据， 只能用当前月前一个月的数据了
        if (inputScaleDetail.isEmpty()) {
            LocalDate now = LocalDate.now();
            while (!latestHistory.isBefore(YearMonth.of(now.getYear(), now.getMonthValue()))) {
                latestHistory = latestHistory.minusMonths(1);
            }
            inputScaleDetail = self.getTaskScale(taskDO.getId(), latestHistory);
        }
        if (inputScaleDetail.isEmpty()) {
            throw new BizException("没有找到对应的历史/预测数据");
        }
        return inputScaleDetail;
    }

    private static List<YearMonth> getSub1AndSub6ByPredictStart(LocalDate predictStart) {
        YearMonth predictYm = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue());
        YearMonth curYm = YearMonth.of(LocalDate.now().getYear(), LocalDate.now().getMonthValue());
        while (predictYm.isAfter(curYm)) {
            predictYm = predictYm.minusMonths(1);
        }
        return Lang.list(
                predictYm.minusMonths(1),
                predictYm.minusMonths(6));
    }

    private List<LongtermPredictInputScaleDO> transFrom(List<LongtermPredictOutputScaleSplitDO> source) {
        List<LongtermPredictInputScaleDO> ret = Lists.newArrayList();
        for (LongtermPredictOutputScaleSplitDO longtermPredictOutputScaleSplitDO : source) {
            ret.add(convertFromOutputScaleSplitDO(longtermPredictOutputScaleSplitDO));
        }
        return ret;
    }

    // 没有instanceType,zoneName
    private LongtermPredictInputScaleDO convertFromOutputScaleSplitDO(
            LongtermPredictOutputScaleSplitDO source) {
        LongtermPredictInputScaleDO ret = new LongtermPredictInputScaleDO();
        ret.setTaskId(source.getTaskId());
        ret.setStatTime(source.getStatTime());
        ret.setYearMonthStr(source.getYearMonthStr());
        ret.setCurCore(source.getCurCore());
        ret.setBizRangeType(source.getBizRangeType());
        ret.setRegionName(source.getRegionName());
        ret.setCountryName(source.getCountryName());
        ret.setCustomhouseTitle(source.getCustomhouseTitle());
        ret.setInstanceFamily(source.getInstanceFamily());
        return ret;
    }

    private static void setDateInfo(LongtermPredictOutputScaleSplitDO oneSplit, YearMonth afterSplitMonth) {
        oneSplit.setYearMonthStr(afterSplitMonth.toString());
        oneSplit.setStatTime(afterSplitMonth.atEndOfMonth());
        int month = afterSplitMonth.getMonthValue();
        int quarter = (month - 1) / 3 + 1;
        int halfYear = (month - 1) / 6 + 1;
        oneSplit.setQuarter(quarter);
        oneSplit.setHalfYear(halfYear);
        oneSplit.setYear(afterSplitMonth.getYear());
    }

    private void setNotSplitColumnInfo(List<LongtermPredictOutputScaleSplitDO> splitRst, Long splitVersionId) {
        splitRst.parallelStream().forEach((o) -> {
            o.setCurCoreOrigin(o.getCurCore());
            o.setSplitVersionId(splitVersionId);
        });
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SplitInfo {

        private String key;
        // 过去半年的coreSum增量
        private BigDecimal diffCoreNum = BigDecimal.ZERO;
        private BigDecimal lastOneMonthCoreNum = BigDecimal.ZERO;
        // 存量拆分的时候使用，用线性增长预估未来的存量
        private BigDecimal future6MonthCoreNum;
        private Boolean IsIncrease;
        private BigDecimal rate;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ScaleSplitInfo extends SplitInfo {

        List<LongtermPredictInputScaleDO> historyData;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ScaleIncreaseSplitInfo extends SplitInfo {

        List<LongtermPredictInputScaleIncreaseDTO> increaseDetail;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IndustryScaleSplitInfo extends SplitInfo {

        List<LongtermPredictInputScaleWithIndustryDO> historyData;
    }


    private void splitColumnInfo(ScaleDataSplit tree) {

        DataSplitOrder splitOrder = tree.getSplitOrder();
        if (tree.getLeafLevel().isEmpty()) {
            return;
        }
        LongtermPredictTaskDO taskDO = tree.getScaleSplitDependDTO().getTaskDO();
        boolean isRegionSplit = Strings.equals(splitOrder.getName(), ScaleDataSplitOrder.REGION_SPLIT_NAME);
        boolean isInstanceTypeSplit = Strings.equals(splitOrder.getName(), ScaleDataSplitOrder.INSTANCE_TYPE);

        LocalDate predictStart = taskDO.getPredictStart();
        // 1. 从历史数据中获取所有地区信息
        List<YearMonth> historyDatePoint = Lang.list(
                YearMonth.of(predictStart.getYear(), predictStart.getMonthValue()).minusMonths(1),
                YearMonth.of(predictStart.getYear(), predictStart.getMonthValue()).minusMonths(6)
        );
        List<LongtermPredictInputScaleDO> inputScaleDetail = Lang.list();
        String strategyType = tree.getCurInputArgs().getStrategyType();
        inputScaleDetail.addAll(getInputOrOutputScaleSub1M(taskDO, strategyType));
        inputScaleDetail.addAll(getInputOrOutputScaleSub6M(taskDO, strategyType));

        // 当前境内只有中国大陆的国家，因此这里拆分region 的时候不用指定到 country
        Map<String, Map<String, ScaleSplitInfo>> columnHistoryMap =
                // 先按之前已经拆分了的数据聚合，为了过滤数据
                ListUtils2.groupAndApply(inputScaleDetail, splitOrder::getSplitPrefixKey,
                        (prefixKey, val) -> {
                            // 按当前的group数据聚合
                            return ListUtils2.groupAndApply(val, splitOrder::getSplitCurKey,
                                    (curSplitKey, list) -> getSplitInfo(curSplitKey, list, historyDatePoint));
                        });
        columnHistoryMap.values().stream()
                .map((o) -> o.values().stream().map((oo) -> (SplitInfo) oo).collect(Collectors.toList()))
                .forEach(SplitServiceImpl::setScaleRate);

        tree.getHistoryList().add(columnHistoryMap);
        // 当前需要拆分的数据
        for (DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> oneLeaf : tree.getLeafLevel()) {
            Map<String, ScaleSplitInfo> splitHistoryMap
                    = columnHistoryMap.get(splitOrder.getSplitPrefixKey(oneLeaf.getData()));
            if (splitHistoryMap == null || splitHistoryMap.isEmpty()) {
                LongtermPredictOutputScaleSplitDO oneSplit = new LongtermPredictOutputScaleSplitDO();
                BeanUtils.copyProperties(oneLeaf.getData(), oneSplit);
                DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> node
                        = DataSplitTreeNode.newNode("没有历史数据的新机型，不拆分", oneSplit);
                node.setNeedSplit(false);
                tree.addNode(oneLeaf, node);
                continue;
            }
            boolean regionNotBlank = Strings.isNotBlank(oneLeaf.getData().getRegionName());
            if (taskDO.isDecisionCategory() && isRegionSplit && regionNotBlank) {
                LongtermPredictOutputScaleSplitDO oneSplit = new LongtermPredictOutputScaleSplitDO();
                BeanUtils.copyProperties(oneLeaf.getData(), oneSplit);
                DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> node
                        = DataSplitTreeNode.newNode("DECISION的region非空不需要拆分", oneSplit);
                node.setNeedSplit(false);
                tree.addNode(oneLeaf, node);
                continue;
            }
            // 可以考虑最小2乘法
            // 1期：
            //  分为2部分，一部分有增量比例拆分， 一部分没有增量的用最近一期的数据来
            //  这样会预估多没有增量的部分，整体向下的时候可能不适用
            // 2期：
            //  计算到的增量+加到期末存量，用得到的存量去计算比例
            // 当前境内只有中国大陆的国家，因此这里拆分region 的时候不用指定到 country
            BigDecimal splitValue = oneLeaf.getData().getCurCore();
            for (Entry<String, ScaleSplitInfo> history : splitHistoryMap.entrySet()) {
                BigDecimal childrenValue = splitValue.multiply(history.getValue().getRate());
                addSplitData2tree(tree, oneLeaf, history.getValue(), childrenValue, splitOrder);
            }
            // 每一次拆分，确定一下当前的拆分结果是不是全部一致的
            throwExceptionIfDiffAbsGTOne(oneLeaf.getChildrenSum(LongtermPredictOutputScaleSplitDO::getCurCore),
                    oneLeaf.getData().getCurCore());
        }
        // 及时设置地域信息，用于web展示, 决策版自带的地域信息，因此排除地域的拆分
        if (isRegionSplit && !taskDO.isDecisionCategory()) {
            setRegionInfo(tree);
        }
        if(isInstanceTypeSplit) {
            setGinsFamily(tree);
        }
    }

    private void setGinsFamily(ScaleDataSplit tree) {
        Map<String, String> allInstanceTypes = cvmPlanService.getInstanceType2InstanceFamily();
        tree.getLeafLevel().forEach((o) -> {
            LongtermPredictOutputScaleSplitDO data = o.getData();
            String instanceType = data.getInstanceType();
            if (instanceType == null){
                return;
            }
            String instanceFamily = allInstanceTypes.get(instanceType);
            data.setInstanceFamily(instanceFamily);
        });
    }

    private void setRegionInfo(ScaleDataSplit tree) {
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = longtermPredictDictService.getRegionNameInfoMap();
        tree.getLeafLevel().forEach((o) -> {
            LongtermPredictOutputScaleSplitDO data = o.getData();
            SoeRegionNameCountryDO regionInfo = regionNameInfoMap.get(data.getRegionName());
            if (regionInfo != null) {
                data.setCountryName(regionInfo.getCountryName());
                data.setCustomhouseTitle(regionInfo.getCustomhouseTitle());
            } else {
                data.setCountryName("");
                data.setCustomhouseTitle("");
            }
        });
    }

    private static void setScaleRate(Collection<SplitInfo> splitInfos) {
        // 大于0 的存量值加起来作为权重
        BigDecimal curTotal = NumberUtils.sum(splitInfos,
                (o) -> o.getFuture6MonthCoreNum().compareTo(BigDecimal.ZERO) > 0
                        ? o.getFuture6MonthCoreNum() : BigDecimal.ZERO);
        if (curTotal.compareTo(BigDecimal.ZERO) > 0) {
            splitInfos.forEach((o) -> {
                if (o.getFuture6MonthCoreNum().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal rate = o.getFuture6MonthCoreNum().divide(curTotal, 10, RoundingMode.HALF_UP);
                    o.setRate(rate);
                } else {
                    o.setRate(BigDecimal.ZERO);
                }
            });
        } else {
            // 如果没有数据了，这里直接用之前的比例去拆分数据
            BigDecimal lastMonthTotal = NumberUtils.sum(splitInfos, SplitInfo::getLastOneMonthCoreNum);
            splitInfos.forEach((o) -> {
                BigDecimal rate = o.getLastOneMonthCoreNum().divide(lastMonthTotal, 10, RoundingMode.HALF_UP);
                o.setRate(rate);
            });
        }
    }

    private static void setRate(Collection<SplitInfo> splitInfos) {
        BigDecimal curTotal = NumberUtils.sum(splitInfos,
                (o) -> o.getIsIncrease() ? o.getDiffCoreNum() : BigDecimal.ZERO);
        splitInfos.forEach((o) -> {
            if (o.getIsIncrease()) {
                BigDecimal rate = o.getDiffCoreNum().divide(curTotal, 10, RoundingMode.HALF_UP);
                o.setRate(rate);
            } else {
                o.setRate(BigDecimal.ZERO);
            }
        });
    }

    private static void addSplitData2tree(
            DataSplitTree<LongtermPredictOutputScaleSplitDO> tree,
            DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> oneLeaf,
            ScaleSplitInfo splitInfo,
            BigDecimal childrenValue,
            DataSplitOrder splitOrder) {
        LongtermPredictOutputScaleSplitDO oneSplit = new LongtermPredictOutputScaleSplitDO();
        BeanUtils.copyProperties(oneLeaf.getData(), oneSplit);
        oneSplit.setCurCore(childrenValue);
        splitOrder.setSplitCurKey(oneSplit, splitInfo.getKey());
        String msg = "%s,使用最近一期的存量+最近半年的变化量来计算比例, 小于0忽略，比例： " + splitInfo.getRate();
        msg = String.format(msg, splitOrder.getName());

        DataSplitTreeNode<LongtermPredictOutputScaleSplitDO> node = DataSplitTreeNode.newNode(msg, oneSplit);
        node.setSplitInfo(splitInfo);
        tree.addNode(oneLeaf, node);
    }

    @SynchronizedHiSpeedCache1Second
    @HiSpeedCache(keyScript = "args[0]", expireSecond = 300)
    public List<LongtermPredictInputArgsDO> getInputArgs(Long taskId) {
        return LongtermPredictInputArgsDO.db().getAll("where task_id=?", taskId);
    }

    @SynchronizedHiSpeedCache1Second
    @HiSpeedCache(keyScript = "args[0]", expireSecond = 300)
    public LongtermPredictTaskDO getTask(Long taskId) {
        return LongtermPredictTaskDO.db().getOne("where id=?", taskId);
    }

    public List<LongtermPredictOutputSplitVersionDO> getSplitVersions(Long taskId) {
        return LongtermPredictOutputSplitVersionDO.db().getAll("where task_id=?", taskId);
    }

    /**
     * @param taskId id
     * @param yearMonths yms
     * @return list
     */
    @SynchronizedHiSpeedCache1Second
    @HiSpeedCache(keyScript = "args[0] + args[1]", expireSecond = 300)
    public List<LongtermPredictInputScaleDO> getTaskScale(Long taskId, List<YearMonth> yearMonths) {
        return TimeUtils.printExecutionTime("getHistory",
                () -> LongtermPredictInputScaleDO.db().getAll("where task_id=? and year_month_str in (?)",
                        taskId, yearMonths.stream().map(YearMonth::toString).distinct().collect(Collectors.toList())));
    }

    /**
     * @param taskId id
     * @param yearMonth yms
     * @return list
     */
    @SynchronizedHiSpeedCache1Second
    @HiSpeedCache(keyScript = "args[0] + args[1]", expireSecond = 300)
    public List<LongtermPredictInputScaleDO> getTaskScale(Long taskId, YearMonth yearMonth) {
        return TimeUtils.printExecutionTime("getHistory",
                () -> LongtermPredictInputScaleDO.db().getAll("where task_id=? and year_month_str = ?",
                        taskId, yearMonth.toString()));
    }

    /**
     * 查询taskId 对应最新的拆分版本
     *
     * @param taskId taskId
     * @param yearMonth ym
     * @return list
     */
    @SynchronizedHiSpeedCache1Second
    @HiSpeedCache(keyScript = "args[0] + args[1]", expireSecond = 300)
    public List<LongtermPredictOutputScaleSplitDO> getTaskScaleSplit(Long taskId, YearMonth yearMonth) {
        LongtermPredictOutputSplitVersionDO version
                = cdLabDbHelper.getOne(LongtermPredictOutputSplitVersionDO.class,
                "where task_id=? order by id desc", taskId);
        if (version == null) {
            throw BizException.makeThrow("没有找到对应的scale split");
        }
        return LongtermPredictOutputScaleSplitDO.db()
                .getAll("where split_version_id=? and year_month_str = ? ",
                        version.getId(), yearMonth.toString());
    }


    //    @HiSpeedCache(expireSecond = 300)
//    @SynchronizedHiSpeedCache1Second
    public List<LongtermPredictInputInstance2deviceRateDO> getInstance2deviceRate() {
        return LongtermPredictInputInstance2deviceRateDO.db().getAll();
    }

    @SynchronizedHiSpeedCache1Second
    @HiSpeedCache(keyScript = "args[0] + args[1]", expireSecond = 300)
    public List<LongtermPredictInputScaleWithIndustryDO> getTaskScaleWithIndustry(Long taskId,
            List<YearMonth> yearMonths) {
        return TimeUtils.printExecutionTime("getHistory",
                () -> LongtermPredictInputScaleWithIndustryDO.db().getAll("where task_id=? and year_month_str in (?)",
                        taskId, yearMonths.stream().map(YearMonth::toString).distinct().collect(Collectors.toList())));
    }
}
