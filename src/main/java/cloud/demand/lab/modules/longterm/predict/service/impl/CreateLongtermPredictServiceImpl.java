package cloud.demand.lab.modules.longterm.predict.service.impl;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.service.CvmPlanService;
import cloud.demand.lab.modules.longterm.predict.dto.DateAndCurCoreDTO;
import cloud.demand.lab.modules.longterm.predict.dto.PurchaseInputDTO;
import cloud.demand.lab.modules.longterm.predict.dto.ScaleInputDTO;
import cloud.demand.lab.modules.longterm.predict.dto.ScaleInputWithIndustryDTO;
import cloud.demand.lab.modules.longterm.predict.dto.XyPurchaseInputDTO;
import cloud.demand.lab.modules.longterm.predict.dto.ZoneRegionInfoDTO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputArgsDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputPurchaseDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputPurchaseWithIndustryDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictInputScaleWithIndustryDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputPurchaseDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictOutputScaleDO;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.predict.enums.Constants;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictCategoryTypeEnum;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import cloud.demand.lab.modules.longterm.predict.service.CreateLongtermPredictService;
import cloud.demand.lab.modules.longterm.predict.service.LongtermPredictDictService;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.CreatePredictTaskReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.InputArgsDTO;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryDefaultInputArgsReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryIncreaseRateReq;
import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryLastInputArgsReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.CreatePredictTaskResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryCategoryForCreateResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryDefaultInputArgsResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryIncreaseRateResp;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryLastInputArgsResp;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.utils.DOInfoReader;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Service
public class CreateLongtermPredictServiceImpl implements CreateLongtermPredictService {

    @Resource
    RedisHelper redisHelper;
    @Resource
    private DBHelper cdLabDbHelper;
    @Resource
    private DBHelper ckcldStdCrpDBHelper;
    @Resource
    private DBHelper ckcubesDBHelper;
    @Resource
    private CvmPlanService cvmPlanService;
    @Resource
    private LongtermPredictDictService longtermPredictDictService;

    protected static final ThreadPoolExecutor updateTaskStatusThreadPool = ThreadPoolUtils.createThreadPool(
            1, 100, 1, "update-task-status");

    @Override
    public QueryCategoryForCreateResp queryCategoryForCreate(QueryCategoryForCreateReq req) {
        List<LongtermPredictCategoryConfigDO> categoryConfigs = cdLabDbHelper.getAll(LongtermPredictCategoryConfigDO.class,
                "where category_type != ?", LongtermPredictCategoryTypeEnum.MERGE.getCode());

        QueryCategoryForCreateResp resp = new QueryCategoryForCreateResp();
        resp.setCategoryList(ListUtils.transform(categoryConfigs, o -> {
            QueryCategoryForCreateResp.Item item = new QueryCategoryForCreateResp.Item();
            item.setCategoryId(o.getId());
            item.setCategoryName(o.getCategory());
            item.setCategoryType(o.getCategoryType());
            item.setCategoryTypeName(LongtermPredictCategoryTypeEnum.getNameByCode(o.getCategoryType()));
            LocalDate startDate = getPredictStartDate(o);
            LocalDate endDate = getPredictEndDate(o, startDate);
            item.setPredictStart(DateUtils.format(startDate, "yyyy-MM"));
            item.setPredictEnd(DateUtils.format(endDate, "yyyy-MM"));
            item.setInputArgDateRanges(getDateRange(startDate, endDate, o.getIntervalMonth()));
            item.setStrategyTypes(ListUtils.transform(StrategyTypeEnum.values(), QueryCategoryForCreateResp.StrategyType::from));
            return item;
        }));
        return resp;
    }

    private List<QueryCategoryForCreateResp.InputArgDateRange> getDateRange(LocalDate startDate, LocalDate endDate,
                                                                            int intervalMonth) {
        if (intervalMonth == 6) { // 每半年作为1个录入周期
            if (startDate.getMonthValue() >= 7) {
                startDate = LocalDate.of(startDate.getYear(), 6, 30);
            } else {
                startDate = LocalDate.of(startDate.getYear() - 1, 12, 31);
            }
            if (endDate.getMonthValue() >= 7) {
                endDate = LocalDate.of(endDate.getYear(), 12, 31);
            } else {
                endDate = LocalDate.of(endDate.getYear(), 6, 30);
            }

            List<QueryCategoryForCreateResp.InputArgDateRange> ranges = new ArrayList<>();

            while (startDate.isBefore(endDate)) {
                QueryCategoryForCreateResp.InputArgDateRange range = new QueryCategoryForCreateResp.InputArgDateRange();
                if (startDate.getMonthValue() == 6) {
                    range.setDateName(startDate.getYear() + "下半年");
                    LocalDate rangeStartDate = startDate.plusDays(1);
                    range.setStartDate(DateUtils.format(rangeStartDate));
                    range.setEndDate(DateUtils.format(rangeStartDate.plusMonths(6).minusDays(1)));
                } else {
                    range.setDateName((startDate.getYear() + 1) + "上半年");
                    LocalDate rangeStartDate = startDate.plusDays(1);
                    range.setStartDate(DateUtils.format(rangeStartDate));
                    range.setEndDate(DateUtils.format(rangeStartDate.plusMonths(6).minusDays(1)));
                }
                ranges.add(range);

                startDate = startDate.plusDays(1).plusMonths(6).minusDays(1);
            }

            return ranges;
        } else if (intervalMonth == 12) { // 每年作为1个录入周期
            startDate = LocalDate.of(startDate.getYear(), 1, 1);
            endDate = LocalDate.of(endDate.getYear(), 12, 31);
            List<QueryCategoryForCreateResp.InputArgDateRange> ranges = new ArrayList<>();
            while (startDate.isBefore(endDate)) {
                QueryCategoryForCreateResp.InputArgDateRange range = new QueryCategoryForCreateResp.InputArgDateRange();
                range.setDateName(startDate.getYear() + "年");
                range.setStartDate(DateUtils.format(startDate));
                range.setEndDate(DateUtils.format(LocalDate.of(startDate.getYear(), 12, 31)));
                ranges.add(range);

                startDate = startDate.plusYears(1);
            }
            return ranges;
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    @SneakyThrows
    public QueryIncreaseRateResp queryIncreaseRate(QueryIncreaseRateReq req) {
        if (req == null || req.getCategoryId() == null) {
            throw new WrongWebParameterException("方案id categoryId 必须提供");
        }
        LongtermPredictCategoryConfigDO categoryConfig = cdLabDbHelper.getOne(LongtermPredictCategoryConfigDO.class,
                "where id=?", req.getCategoryId());
        if (categoryConfig == null) {
            throw new WrongWebParameterException("方案不存在，可能已经被删除了，请刷新页面重试");
        }

        QueryIncreaseRateResp resp = new QueryIncreaseRateResp();
        resp.setItems(new ArrayList<>());

        LocalDate thisMonthEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        LocalDate lastMonthEnd = thisMonthEnd.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        LocalDate last6MonthEnd = thisMonthEnd.minusMonths(7).with(TemporalAdjusters.lastDayOfMonth());

        BigDecimal increaseRate = getIncreaseRate(categoryConfig, last6MonthEnd, lastMonthEnd);
        if (increaseRate != null) {
            QueryIncreaseRateResp.Item item = new QueryIncreaseRateResp.Item();
            item.setIncreaseRate(increaseRate);
            item.setDateName("近半年(" + DateUtils.format(last6MonthEnd.plusDays(1)) + "~" + DateUtils.format(lastMonthEnd) + ")增速");
            resp.getItems().add(item);
        }

        return resp;
    }

    @SneakyThrows
    private BigDecimal getIncreaseRate(LongtermPredictCategoryConfigDO categoryConfig, LocalDate startDate, LocalDate endDate) {
        String inputScaleSql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/input/input_scale_sum.sql");
        inputScaleSql = inputScaleSql.replace("${CATEGORY_CONDITION}", categoryConfig.getWhereSql());
        List<DateAndCurCoreDTO> curCores = ckcldStdCrpDBHelper.getRaw(DateAndCurCoreDTO.class,
                inputScaleSql, ListUtils.of(startDate, endDate));
        if (curCores.size() == 2) {
            ListUtils.sortAscNullLast(curCores, o -> o.getStatTime());
            return curCores.get(1).getCurCore().subtract(curCores.get(0).getCurCore())
                    .divide(curCores.get(0).getCurCore(), 3, RoundingMode.HALF_UP);
        } else {
            return null;
        }
    }

    @Override
    public QueryLastInputArgsResp queryLastInputArgs(QueryLastInputArgsReq req) {
        QueryLastInputArgsResp resp = new QueryLastInputArgsResp();
        resp.setInputArgs(new ArrayList<>());
        if (req.getCategoryId() == null) {
            return resp;
        }

        LongtermPredictTaskDO taskDO = cdLabDbHelper.getOne(LongtermPredictTaskDO.class,
                "where category_id=? and is_enable=1 order by predict_start desc", req.getCategoryId());
        if (taskDO == null) {
            return resp;
        }

        List<LongtermPredictInputArgsDO> inputArgs = cdLabDbHelper.getAll(
                LongtermPredictInputArgsDO.class, "where task_id=?", taskDO.getId());
        resp.setInputArgs(ListUtils.transform(inputArgs, InputArgsDTO::from));
        return resp;
    }

    @Override
    public QueryDefaultInputArgsResp queryDefaultInputArgs(QueryDefaultInputArgsReq req) {
        if (req.getReferenceMonths() == null || req.getReferenceMonths() < 1) {
            req.setReferenceMonths(6);
        }

        LongtermPredictCategoryConfigDO categoryConfig = cdLabDbHelper.getOne(LongtermPredictCategoryConfigDO.class,
                "where id=?", req.getCategoryId());
        if (categoryConfig == null) {
            throw new WrongWebParameterException("方案不存在，可能已经被删除了，请刷新页面重试");
        }

        LocalDate thisMonthEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        LocalDate lastMonthEnd = thisMonthEnd.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        LocalDate lastXMonthEnd = thisMonthEnd.minusMonths(1 + req.getReferenceMonths()).with(TemporalAdjusters.lastDayOfMonth());

        QueryDefaultInputArgsResp resp = new QueryDefaultInputArgsResp();
        resp.setInputArgs(new ArrayList<>());
        BigDecimal increaseRate = getIncreaseRate(categoryConfig, lastXMonthEnd, lastMonthEnd);
        if (increaseRate == null) {
            return resp;
        }
        // 增长率要则算成半年的
        increaseRate = increaseRate.multiply(BigDecimal.valueOf(6.0/req.getReferenceMonths()));
        increaseRate = increaseRate.setScale(3, RoundingMode.HALF_UP);

        // 查询需要录入的数据范围
        LocalDate startDate = getPredictStartDate(categoryConfig);
        LocalDate endDate = getPredictEndDate(categoryConfig, startDate);
        List<QueryCategoryForCreateResp.InputArgDateRange> dateRange = getDateRange(startDate, endDate, categoryConfig.getIntervalMonth());
        StrategyTypeEnum[] strategyEnums = StrategyTypeEnum.values();

        for (StrategyTypeEnum strategyEnum : strategyEnums) {
            for (QueryCategoryForCreateResp.InputArgDateRange range : dateRange) {
                InputArgsDTO inputArgs = new InputArgsDTO();
                inputArgs.setStrategyType(strategyEnum.getCode());
                inputArgs.setDateName(range.getDateName());
                inputArgs.setStartDate(range.getStartDate());
                inputArgs.setEndDate(range.getEndDate());
                if (strategyEnum == StrategyTypeEnum.EXTREME) {
                    inputArgs.setScaleGrowthRate(increaseRate.add(new BigDecimal("0.01")));
                } else if (strategyEnum == StrategyTypeEnum.CAUTIOUS) {
                    inputArgs.setScaleGrowthRate(increaseRate.subtract(new BigDecimal("0.01")));
                } else {
                    inputArgs.setScaleGrowthRate(increaseRate);
                }
                inputArgs.setReplaceRate(categoryConfig.getDefaultReplaceRate());
                inputArgs.setPurchaseRate(categoryConfig.getDefaultPurchaseRate());

                resp.getInputArgs().add(inputArgs);
            }
        }

        return resp;
    }

    @SneakyThrows
    @Override
    @Synchronized(keyScript = "args[0].categoryId", customExceptionMessage = "当前方案正在创建任务中，暂不支持并发给同一方案创建预测任务，请稍后重试")
    @Transactional(value = "cdlabTransactionManager")
    public CreatePredictTaskResp createPredictTask(CreatePredictTaskReq req) {
        // 1. 检查方案是否存在
        LongtermPredictCategoryConfigDO categoryConfig = getCategoryConfig(req.getCategoryId());

        // 2. 根据方案创建对应的预测任务，设置状态为新建
        if (req.getIsUseCache() == null) {
            req.setIsUseCache(true);
        }
        LongtermPredictTaskDO task = createTask(categoryConfig, req.getCategoryId(), req.getIsEnable(), req.getIsUseCache());
        // 2.1 检查方案输入参数是否和预测时间范围对应，对应了再创建任务
        checkInputArgs(req, task);
        // 2.2 如果预测月份的方案已经isEnable，则覆盖它
        if (req.getIsEnable() != null && req.getIsEnable()) {
            cdLabDbHelper.updateAll(LongtermPredictTaskDO.class, "is_enable=2",
                    "where is_enable=1 and category_id=? and predict_start=?",
                      task.getCategoryId(), task.getPredictStart());
            task.setIsEnable(true);
        }
        // 2.3 创建任务
        cdLabDbHelper.insert(task);

        // 3. 保存方案输入参数
        List<LongtermPredictInputArgsDO> inputArgsDO =
                ListUtils.transform(req.getInputArgs(), o -> InputArgsDTO.trans(o, task.getId()));
        cdLabDbHelper.insertBatchWithoutReturnId(inputArgsDO);

        // 6. 发送创建任务消息，等待异步处理
        Runnable sendMsg = () -> redisHelper.send(Constants.REDIS_QUEUE_LONGTERM_PREDICT_TASK, task.getId().toString(), 600);
        boolean submit = cdLabDbHelper.executeAfterCommit(sendMsg);
        if (!submit) {
            sendMsg.run();
        }

        CreatePredictTaskResp resp = new CreatePredictTaskResp();
        resp.setTaskId(task.getId());
        return resp;
    }

    /**查找方案信息*/
    private LongtermPredictCategoryConfigDO getCategoryConfig(Long categoryId) {
        if (categoryId == null) {
            throw new WrongWebParameterException("方案id(categoryId)不能为空");
        }
        LongtermPredictCategoryConfigDO categoryConfig = cdLabDbHelper.getOne(LongtermPredictCategoryConfigDO.class,
                "where id=?", categoryId);
        if (categoryConfig == null) {
            throw new WrongWebParameterException("方案不存在");
        }
        return categoryConfig;
    }

    /**都以月末为表示*/
    protected LocalDate getPredictStartDate(LongtermPredictCategoryConfigDO categoryConfigDO) {
        if ("CUR_MONTH".equals(categoryConfigDO.getPredictStart())) {
            return LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        } else {
            LocalDate startDate = DateUtils.parseLocalDate(categoryConfigDO.getPredictStart());
            if (startDate == null) {
                return LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()); // 默认当月
            }
            return startDate.with(TemporalAdjusters.lastDayOfMonth());
        }
    }

    /**都以月末为表示*/
    protected LocalDate getPredictEndDate(LongtermPredictCategoryConfigDO categoryConfigDO, LocalDate startDate) {
        if (categoryConfigDO.getPredictEnd() != null && categoryConfigDO.getPredictEnd().startsWith("LAST_MONTH_")) {
            String month = categoryConfigDO.getPredictEnd().substring("LAST_MONTH_".length());
            Integer m = NumberUtils.parseInt(month);
            if (m != null && m >= 0) {
                return startDate.plusMonths(m).with(TemporalAdjusters.lastDayOfMonth());
            } else {
                return startDate.plusMonths(18).with(TemporalAdjusters.lastDayOfMonth()); // 默认18个月
            }
        } else {
            return DateUtils.parseLocalDate(categoryConfigDO.getPredictEnd()).with(TemporalAdjusters.lastDayOfMonth());
        }
    }

    /**特别说明：这个方案也被CreateLongtermDecisionPredictService引用*/
    @SneakyThrows
    protected LongtermPredictTaskDO createTask(LongtermPredictCategoryConfigDO categoryConfig,
                                               Long categoryId, Boolean isEnable, Boolean isUseCache) {
        LongtermPredictTaskDO task = new LongtermPredictTaskDO();
        task.setCategoryId(categoryId);
        task.setCategoryName(categoryConfig.getCategory());
        task.setCategoryType(categoryConfig.getCategoryType());
        task.setIsEnable(isEnable != null && isEnable);
        task.setTaskStatus(LongtermPredictTaskStatusEnum.NEW.getCode());
        task.setPredictStart(getPredictStartDate(categoryConfig));
        task.setPredictEnd(getPredictEndDate(categoryConfig, task.getPredictStart()));
        task.setConditionSql(categoryConfig.getWhereSql());
        task.setUseCache(isUseCache);

        // 存量的查询sql1
        String inputScaleSql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/input/input_scale.sql");
        inputScaleSql = inputScaleSql.replace("${CATEGORY_CONDITION}", task.getConditionSql());
        inputScaleSql = inputScaleSql.replace("${DATE_RANGE}", "stat_time<'" + task.getPredictStart() + "'");
        task.setInputSql(inputScaleSql);

        // 存量的查询sql2，带行业
        String inputScaleWithIndustrySql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/input/input_scale_with_industry.sql");
        inputScaleWithIndustrySql = inputScaleWithIndustrySql.replace("${CATEGORY_CONDITION}", task.getConditionSql());
        inputScaleWithIndustrySql = inputScaleWithIndustrySql.replace("${DATE_RANGE}", "stat_time<'" + task.getPredictStart() + "'");
        task.setInputWithIndustrySql(inputScaleWithIndustrySql);

        // 采购量的查询sql
        String inputPurchaseSql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/input/input_purchase.sql");
        inputPurchaseSql = inputPurchaseSql.replace("${CATEGORY_CONDITION}", categoryConfig.getPurchaseWhereSql());
        inputPurchaseSql = inputPurchaseSql.replace("${DATE_RANGE}", "stat_time<'" + task.getPredictStart() + "'");
        task.setPurchaseInputSql(inputPurchaseSql);

        // 星云的采购量的查询sql，带行业
        String xyInputPurchaseSql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/input/input_purchase_xy.sql");
        xyInputPurchaseSql = xyInputPurchaseSql.replace("${CATEGORY_CONDITION}", categoryConfig.getXyPurchaseWhereSql());
        xyInputPurchaseSql = xyInputPurchaseSql.replace("${DATE_RANGE}", "stat_time<='" +
                task.getPredictStart().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()) + "'"); // 由于这个精确到天，所以是一个月前的
        task.setXyPurchaseInputSql(xyInputPurchaseSql);

        task.setDimsName(categoryConfig.getDimsName());
        task.setIntervalMonth(categoryConfig.getIntervalMonth());
        task.setScopeCustomer(categoryConfig.getScopeCustomer());
        task.setScopeProduct(categoryConfig.getScopeProduct());
        task.setScopeResourcePool(categoryConfig.getScopeResourcePool());

        task.setSplitConfig(categoryConfig.getSplitConfig());

        return task;
    }

    /**检查输入参数是否正确*/
    private void checkInputArgs(CreatePredictTaskReq req, LongtermPredictTaskDO task) {
        if (ListUtils.isEmpty(req.getInputArgs())) {
            throw new WrongWebParameterException("预测输入参数不能为空");
        }
        for (InputArgsDTO arg : req.getInputArgs()) {
            if (arg == null) {
                throw new WrongWebParameterException("预测输入参数不能为空");
            }
            LocalDate startDate = DateUtils.parseLocalDate(arg.getStartDate());
            LocalDate endDate = DateUtils.parseLocalDate(arg.getEndDate());
            if (startDate == null || endDate == null) {
                throw new WrongWebParameterException("预测输入参数的开始时间和结束时间不能为空或格式错误，必须为yyyy-MM-dd");
            }
            StrategyTypeEnum strategyTypeEnum = StrategyTypeEnum.getByCode(arg.getStrategyType());
            if (strategyTypeEnum == null) {
                throw new WrongWebParameterException("预测输入参数的策略类型不正确");
            }
            if (arg.getScaleGrowthRate() == null) {
                throw new WrongWebParameterException("预测输入参数必须设置预测量增长率");
            }
            if (arg.getReplaceRate() == null) {
                throw new WrongWebParameterException("预测输入参数必须设置替换比例");
            }
            if (arg.getPurchaseRate() == null) {
                throw new WrongWebParameterException("预测输入参数必须设置采购系数");
            }
        }

        Map<String, List<InputArgsDTO>> strategyInputs =
                ListUtils.toMapList(req.getInputArgs(), o -> o.getStrategyType(), o -> o);
        for (StrategyTypeEnum strategyTypeEnum : StrategyTypeEnum.values()) {
            List<InputArgsDTO> inputArgs1 = strategyInputs.get(strategyTypeEnum.getCode());
            if (ListUtils.isEmpty(inputArgs1)) {
                throw new WrongWebParameterException("预测输入参数必须提供" + strategyTypeEnum.getName() + "的输入参数");
            }

            ListUtils.sortAscNullLast(inputArgs1, InputArgsDTO::getStartDate);

            // req的input 参数必须覆盖预测的开始和结束
            LocalDate predictStart = task.getPredictStart();
            LocalDate predictEnd = task.getPredictEnd();

            LocalDate start = DateUtils.parseLocalDate(inputArgs1.get(0).getStartDate());
            if (start.isAfter(predictStart)) {
                throw new WrongWebParameterException("预测输入参数的时间范围不足，请补全");
            }
            LocalDate end = DateUtils.parseLocalDate(inputArgs1.get(inputArgs1.size() - 1).getEndDate());
            if (end.isBefore(predictEnd)) {
                throw new WrongWebParameterException("预测输入参数的时间范围不足，请补全");
            }

            // 检查req的input的时间范围是连续的
            for (int i = 1; i < inputArgs1.size(); i++) {
                LocalDate preEnd = DateUtils.parseLocalDate(inputArgs1.get(i - 1).getEndDate());
                LocalDate curStart = DateUtils.parseLocalDate(inputArgs1.get(i).getStartDate());
                if (!preEnd.plusDays(1).equals(curStart)) {
                    throw new WrongWebParameterException("预测输入参数的时间范围不是连续的，请检查");
                }
            }
        }
    }

    protected LongtermPredictTaskDO getTaskAndSetStatus(Long taskId) {
        LongtermPredictTaskDO task = cdLabDbHelper.getOne(LongtermPredictTaskDO.class, "where id=?", taskId);
        if (task == null) {
            throw new BizException("预测任务" + taskId + "不存在");
        }
        if (!(LongtermPredictTaskStatusEnum.NEW.getCode().equals(task.getTaskStatus())
                || LongtermPredictTaskStatusEnum.PREDICT_FAIL.getName().equals(task.getTaskStatus())
                || LongtermPredictTaskStatusEnum.SPLIT_FAIL.getName().equals(task.getTaskStatus()))) {
            throw new BizException("预测任务" + taskId + "状态为" + task.getTaskStatus() + "，不能运行");
        }

        task.setTaskStartTime(LocalDateTime.now());
        task.setTaskStatus(LongtermPredictTaskStatusEnum.RUNNING.getCode());
        try {
            task.setRunIp(StringTools.join(",", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            task.setRunIp("EXCEPTION:" + e.getMessage());
        }
        updateTaskStatusThreadPool.submit(() -> {
            cdLabDbHelper.update(task);
        });

        return task;
    }

    protected void preparedTaskData(LongtermPredictTaskDO task) {
        try {
            // 2.1 保存方案输入scale数据，共2张表
            boolean isDone = false;
            if (task.getUseCache() != null && task.getUseCache()) {
                LongtermPredictTaskDO existTaskDO = cdLabDbHelper.getOne(LongtermPredictTaskDO.class,
                        "where category_id=? and task_status=? and predict_start=? and input_sql=? order by id desc",
                        task.getCategoryId(), LongtermPredictTaskStatusEnum.SUCCESS.getCode(), task.getPredictStart(), task.getInputSql());
                if (existTaskDO != null) {
                    copyInputScale(existTaskDO.getId(), task.getId());

                    List<LongtermPredictInputScaleWithIndustryDO> inputs2 =
                            cdLabDbHelper.getAll(LongtermPredictInputScaleWithIndustryDO.class, "where task_id=?", existTaskDO.getId());
                    inputs2.forEach(o -> {
                        o.setId(null);
                        o.setTaskId(task.getId());
                    });
                    cdLabDbHelper.insertBatchWithoutReturnId(inputs2);

                    task.setInputActualUseCache(true);
                    isDone = true;
                }
            }

            if (!isDone) {
                List<ScaleInputDTO> scaleInputs = ckcldStdCrpDBHelper.getRaw(ScaleInputDTO.class, task.getInputSql());
                Map<String, String> instanceTypeToInstanceFamily = cvmPlanService.getInstanceType2InstanceFamily();
                Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
                Map<String, String> regionToCountryMap = MapUtils.transform(regionNameInfoMap, SoeRegionNameCountryDO::getCountryName);
                List<LongtermPredictInputScaleDO> scaleInputDOs = ListUtils.transform(
                        scaleInputs, o -> ScaleInputDTO.transFrom(o, task.getId(), instanceTypeToInstanceFamily, regionToCountryMap));
                task.setInputActualUseCache(false);
                int i = cdLabDbHelper.insertBatchWithoutReturnId(scaleInputDOs);

                System.out.println(i);
                List<ScaleInputWithIndustryDTO> scaleWithIndustryInputs =
                        ckcldStdCrpDBHelper.getRaw(ScaleInputWithIndustryDTO.class, task.getInputWithIndustrySql());
                List<LongtermPredictInputScaleWithIndustryDO> scaleInputWithIndustryDOs = ListUtils.transform(
                        scaleWithIndustryInputs, o -> ScaleInputWithIndustryDTO.transFrom(o, task.getId()));
                cdLabDbHelper.insertBatchWithoutReturnId(scaleInputWithIndustryDOs);
            }
        } catch (Exception e) {
            log.error("预测任务taskId={}处理scale输入数据失败", task.getId(), e);
            task.setTaskStatus(LongtermPredictTaskStatusEnum.PREDICT_FAIL.getCode());
            task.setErrMsg("预测任务处理scale input数据失败:" + e.getMessage());
            task.setTaskEndTime(LocalDateTime.now());

            updateTaskStatusThreadPool.submit(() -> {
                cdLabDbHelper.update(task);
            });
            throw new RuntimeException(e); // 触发事务回滚
        }

        try {
            // 2.2 保存方案输入purchase数据
            boolean isDone = false;
            if (task.getUseCache() != null && task.getUseCache()) {
                LongtermPredictTaskDO existTaskDO = cdLabDbHelper.getOne(LongtermPredictTaskDO.class,
                        "where category_id=? and task_status=? and predict_start=? and purchase_input_sql=? order by id desc",
                        task.getCategoryId(), LongtermPredictTaskStatusEnum.SUCCESS.getCode(), task.getPredictStart(), task.getPurchaseInputSql());
                if (existTaskDO != null) {
                    List<LongtermPredictInputPurchaseDO> inputs =
                            cdLabDbHelper.getAll(LongtermPredictInputPurchaseDO.class, "where task_id=?", existTaskDO.getId());
                    inputs.forEach(o -> {
                        o.setId(null);
                        o.setTaskId(task.getId());
                    });
                    cdLabDbHelper.insertBatchWithoutReturnId(inputs);
                    task.setPurchaseInputActualUseCache(true);
                    isDone = true;
                }
            }

            if (!isDone) {
                List<PurchaseInputDTO> purchaseInputs = ckcubesDBHelper.getRaw(PurchaseInputDTO.class, task.getPurchaseInputSql());
                Map<String, String> deviceTypeToInstanceTypeMap = longtermPredictDictService.getDeviceTypeToInstanceTypeMap();
                Map<String, String> instanceTypeToInstanceFamily = cvmPlanService.getInstanceType2InstanceFamily();
                Map<String, ZoneRegionInfoDTO> campus2ZoneInfoMap = longtermPredictDictService.getCampusToZoneInfo();
                Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
                Map<String, String> regionToCountryMap = MapUtils.transform(regionNameInfoMap, SoeRegionNameCountryDO::getCountryName);
                List<LongtermPredictInputPurchaseDO> inputPurchaseDOs = ListUtils.transform(purchaseInputs, o -> PurchaseInputDTO.trans(o, task.getId(), campus2ZoneInfoMap,
                        regionToCountryMap, deviceTypeToInstanceTypeMap, instanceTypeToInstanceFamily));
                task.setPurchaseInputActualUseCache(false);
                cdLabDbHelper.insertBatchWithoutReturnId(inputPurchaseDOs);
            }
        } catch (Exception e) {
            log.error("预测任务taskId={}处理purchase输入数据失败", task.getId(), e);
            task.setTaskStatus(LongtermPredictTaskStatusEnum.PREDICT_FAIL.getCode());
            task.setErrMsg("预测任务处理purchase input数据失败:" + e.getMessage());
            task.setTaskEndTime(LocalDateTime.now());

            updateTaskStatusThreadPool.submit(() -> {
                cdLabDbHelper.update(task);
            });
            throw new RuntimeException(e); // 触发事务回滚
        }

        try {
            // 2.3 保存方案输入星云purchase数据
            boolean isDone = false;
            if (task.getUseCache() != null && task.getUseCache()) {
                LongtermPredictTaskDO existTaskDO = cdLabDbHelper.getOne(LongtermPredictTaskDO.class,
                        "where category_id=? and task_status=? and predict_start=? and xy_purchase_input_sql=? order by id desc",
                        task.getCategoryId(), LongtermPredictTaskStatusEnum.SUCCESS.getCode(), task.getPredictStart(), task.getXyPurchaseInputSql());
                if (existTaskDO != null) {
                    List<LongtermPredictInputPurchaseWithIndustryDO> inputs =
                            cdLabDbHelper.getAll(LongtermPredictInputPurchaseWithIndustryDO.class, "where task_id=?", existTaskDO.getId());
                    inputs.forEach(o -> {
                        o.setId(null);
                        o.setTaskId(task.getId());
                    });
                    cdLabDbHelper.insertBatchWithoutReturnId(inputs);
                    task.setXyPurchaseInputActualUseCache(true);
                    isDone = true;
                }
            }

            if (!isDone) {
                List<XyPurchaseInputDTO> purchaseInputs = ckcubesDBHelper.getRaw(XyPurchaseInputDTO.class, task.getXyPurchaseInputSql());
                Map<String, String> deviceTypeToInstanceTypeMap = longtermPredictDictService.getDeviceTypeToInstanceTypeMap();
                Map<String, String> instanceTypeToInstanceFamily = cvmPlanService.getInstanceType2InstanceFamily();
                Map<String, ZoneRegionInfoDTO> campus2ZoneInfoMap = longtermPredictDictService.getCampusToZoneInfo();
                Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
                Map<String, String> regionToCountryMap = MapUtils.transform(regionNameInfoMap, SoeRegionNameCountryDO::getCountryName);
                List<LongtermPredictInputPurchaseWithIndustryDO> inputPurchaseDOs = ListUtils.transform(
                        purchaseInputs, o -> XyPurchaseInputDTO.trans(o, task.getId(), campus2ZoneInfoMap,
                                regionToCountryMap, deviceTypeToInstanceTypeMap, instanceTypeToInstanceFamily));
                task.setXyPurchaseInputActualUseCache(false);
                cdLabDbHelper.insertBatchWithoutReturnId(inputPurchaseDOs);
            }
        } catch (Exception e) {
            log.error("预测任务taskId={}处理xy purchase输入数据失败", task.getId(), e);
            task.setTaskStatus(LongtermPredictTaskStatusEnum.PREDICT_FAIL.getCode());
            task.setErrMsg("预测任务处理xy purchase input数据失败:" + e.getMessage());
            task.setTaskEndTime(LocalDateTime.now());

            updateTaskStatusThreadPool.submit(() -> {
                cdLabDbHelper.update(task);
            });
            throw new RuntimeException(e); // 触发事务回滚
        }
    }


    @Override
    @Synchronized(keyScript = "args[0]", waitLockMillisecond = 100)
    @Transactional(value = "cdlabTransactionManager",isolation = Isolation.READ_UNCOMMITTED)
    public void doRunTask(Long taskId) {
        long start = System.currentTimeMillis();
        log.info("start run longterm predict task:{}", taskId);

        // 1. 判断任务的状态，并设置任务状态为运行中，异步不要事务
        LongtermPredictTaskDO task = getTaskAndSetStatus(taskId);

        // 2. 处理任务的入参
        preparedTaskData(task);

        // 3. 准备输入参数
        List<LongtermPredictInputArgsDO> inputArgs = cdLabDbHelper.getAll(LongtermPredictInputArgsDO.class,
                "where task_id=?", taskId);
        // 按策略分开预测
        Map<String, List<LongtermPredictInputArgsDO>> strategyTypeInputs =
                ListUtils.toMapList(inputArgs, LongtermPredictInputArgsDO::getStrategyType, o -> o);
        strategyTypeInputs.values().forEach(o -> ListUtils.sortAscNullLast(o, LongtermPredictInputArgsDO::getStartDate));

        // 4. 开始运行任务，预估存量规模总量
        try {
            for (Map.Entry<String, List<LongtermPredictInputArgsDO>> e :strategyTypeInputs.entrySet()) {
                String strategy = e.getKey();

                for (LongtermPredictInputArgsDO arg : e.getValue()) {
                    LocalDate lastDayOfPeriod = arg.getStartDate().minusDays(1);
                    LongtermPredictOutputScaleDO lastOutput = cdLabDbHelper.getOne(LongtermPredictOutputScaleDO.class,
                            "where task_id=? and strategy_type=? and stat_time=?",
                            taskId, strategy, lastDayOfPeriod);
                    BigDecimal lastCurCore = null;
                    if (lastOutput == null) { // 如果最后的存量不存在，那就去input找
                        lastCurCore = cdLabDbHelper.getRawOne(BigDecimal.class,
                            "select sum(cur_core) from longterm_predict_input_scale where deleted=0 and task_id=? and stat_time=?",
                                taskId, lastDayOfPeriod);
                    } else {
                        lastCurCore = lastOutput.getCurCore();
                    }

                    BigDecimal newLastCurCore = lastCurCore.multiply(arg.getScaleGrowthRate().add(BigDecimal.ONE));
                    LongtermPredictOutputScaleDO out = new LongtermPredictOutputScaleDO();
                    out.setTaskId(taskId);
                    out.setInputArgsId(arg.getId());
                    out.setStrategyType(strategy);
                    out.setDateName(arg.getDateName());
                    out.setStatTime(arg.getEndDate());
                    out.setCurCore(newLastCurCore);
                    out.setStartStatTime(arg.getStartDate());
                    out.setStartCurCore(lastCurCore);
                    cdLabDbHelper.insert(out);
                }
            }
        } catch (Exception e) {
            log.error("预测任务taskId={}运行失败", taskId, e);
            task.setTaskStatus(LongtermPredictTaskStatusEnum.PREDICT_FAIL.getCode());
            task.setErrMsg("预测存量规模发生错误:" + e.getMessage());
            task.setTaskEndTime(LocalDateTime.now());

            updateTaskStatusThreadPool.submit(() -> {
                cdLabDbHelper.update(task);
            });
            throw new RuntimeException(e); // 触发事务回滚
        }

        // 3. 预测采购量总量
        try {
            for (Map.Entry<String, List<LongtermPredictInputArgsDO>> e :strategyTypeInputs.entrySet()) {
                String strategy = e.getKey();

                for (LongtermPredictInputArgsDO arg : e.getValue()) {
                    LongtermPredictOutputScaleDO scale = cdLabDbHelper.getOne(LongtermPredictOutputScaleDO.class,
                            "where task_id=? and input_args_id=?", taskId, arg.getId());
                    // 公式：采购量 = (期末减期初 + 期初 * 替换比例) / 采购系数
                    BigDecimal purchaseNum =
                            (scale.getCurCore().subtract(scale.getStartCurCore()).add(scale.getStartCurCore().multiply(arg.getReplaceRate())))
                                    .divide(arg.getPurchaseRate(), 2, RoundingMode.HALF_UP);
                    LongtermPredictOutputPurchaseDO purchase = new LongtermPredictOutputPurchaseDO();
                    purchase.setTaskId(taskId);
                    purchase.setInputArgsId(arg.getId());
                    purchase.setStrategyType(strategy);
                    purchase.setDateName(arg.getDateName());
                    purchase.setStartDate(arg.getStartDate());
                    purchase.setEndDate(arg.getEndDate());
                    purchase.setPurchaseCore(purchaseNum);

                    cdLabDbHelper.insert(purchase);
                }
            }
        } catch (Exception e) {
            log.error("预测任务taskId={}运行失败", taskId, e);
            task.setTaskStatus(LongtermPredictTaskStatusEnum.PREDICT_FAIL.getCode());
            task.setErrMsg("预测采购总量发生错误:" +e.getMessage());
            task.setTaskEndTime(LocalDateTime.now());

            updateTaskStatusThreadPool.submit(() -> {
                cdLabDbHelper.update(task);
            });
            throw new RuntimeException(e); // 触发事务回滚
        }

        // 4. 修改任务状态，成功
        task.setTaskStatus(LongtermPredictTaskStatusEnum.PREDICT_SUCCESS.getCode());
        task.setTaskEndTime(LocalDateTime.now());
        task.setCostMs(System.currentTimeMillis() - start);
        cdLabDbHelper.update(task);
        log.info("predict task taskId={} success end.", taskId);
    }


    /**由于scale有6万多行数据，因此在db进行复制，速度更快*/
    private void copyInputScale(Long sourceTaskId, Long targetTaskId) {
        List<Field> notKeyColumns = DOInfoReader.getNotKeyColumns(LongtermPredictInputScaleDO.class);
        String columns = StringTools.join(",", ListUtils.transform(notKeyColumns,
                o -> o.getAnnotation(Column.class).value()));
        String sql = "insert into longterm_predict_input_scale(" +
                columns + ") select " +
                columns.replace("task_id", targetTaskId.toString()) +
                " from longterm_predict_input_scale where task_id=?";

        cdLabDbHelper.executeRaw(sql, sourceTaskId);
    }

}
