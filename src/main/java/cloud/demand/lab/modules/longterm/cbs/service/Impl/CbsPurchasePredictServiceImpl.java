package cloud.demand.lab.modules.longterm.cbs.service.Impl;

import cloud.demand.lab.common.utils.EStream;
import cloud.demand.lab.modules.longterm.cbs.dto.CbsDemandMarketDTO;
import cloud.demand.lab.modules.longterm.cbs.dto.CbsPurchaseDTO;
import cloud.demand.lab.modules.longterm.cbs.entity.CbsRatioDetailDO;
import cloud.demand.lab.modules.longterm.cbs.service.CbsPurchasePredictService;
import cloud.demand.lab.modules.longterm.cbs.service.CbsRatioService;
import cloud.demand.lab.modules.longterm.cbs.web.req.scale_predict.QueryCbsPurchaseTotalReq;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.QueryCbsPurchaseHalfYearAccumulateResp;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.QueryCbsPurchaseHalfYearAccumulateResp.HalfYearAccumulateItem;
import cloud.demand.lab.modules.longterm.cbs.web.resp.scale_predict.QueryCbsPurchaseTotalResp;
import cloud.demand.lab.modules.longterm.predict.entity.LongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.io.IOUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CbsPurchasePredictServiceImpl implements CbsPurchasePredictService {

    @Resource
    private CbsRatioService cbsRatioService;

    @Resource
    private DBHelper cdLabDbHelper;

    @Resource
    private DBHelper ckcubesDBHelper;

    /**
     * 右侧两个框
     * @param req
     * @return
     */
    @Override
    public QueryCbsPurchaseTotalResp queryCbsPurchaseTotal(QueryCbsPurchaseTotalReq req) {
        //构建响应对象
        QueryCbsPurchaseTotalResp resp=new QueryCbsPurchaseTotalResp();
        //1. 获取长期预测任务信息
        LongtermPredictTaskDO longtermPredictTaskDO = cdLabDbHelper.getOne(LongtermPredictTaskDO.class, "where id = ?", req.getTaskId());
        if(longtermPredictTaskDO==null)throw new RuntimeException("查询不到预测认为信息，请检查categoryId"+req.getRatioCategoryId());

        //生成sql查询的参数
        Map<String, Object> params = getPurchaseParams(req, longtermPredictTaskDO);


        // 获取配比方案明细
        List<CbsRatioDetailDO> ratioDetailList = cdLabDbHelper.getAll(CbsRatioDetailDO.class, "where ratio_category_id=?", req.getRatioCategoryId());
        if(ratioDetailList==null||ratioDetailList.isEmpty())throw new RuntimeException("查询不到配比方案明细"+req.getRatioCategoryId()+"下的配比明细");
        Map<String, BigDecimal> ratioMap = cbsRatioService.getCommonRatioMap(req.getTaskId().toString());
        //2. 查询cvm的预测采购量
        List<CbsPurchaseDTO> cbsPredictDTOList = queryCvmPurchasePredictDTO(params);

        //3. 查询cvm的存量采购量
        List<CbsPurchaseDTO> cbsFinishDTOList = queryCvmPurchaseFinishDTO(params);
        //4. 合并cvm预测采购量与cvm存量采购量
        List<CbsPurchaseDTO> strategyItemList = mergeAndCalculate(cbsPredictDTOList, cbsFinishDTOList);
        //去除所有计算完成的存量数据
        strategyItemList.removeIf(dto->dto.getStrategyType()==null&&!dto.getCvmInstanceType().equals("(默认)"));
        //5. 将核心数转换为磁盘数
        List<CbsPurchaseDTO> convertedlist = convertSumCoreToSumDisk(req.getRatioCategoryId(), strategyItemList);

        //调试数据
        //resp.setCbsPurchaseDTOList(convertedlist);
        //6. 创建年数据
        Map<Integer, Map<String, QueryCbsPurchaseTotalResp.CbsStrategyItem>> strategyItemMap=processStrategyItems(convertedlist,params);
        List<QueryCbsPurchaseTotalResp.CbsYearItem> yearItems = buildYearItems(strategyItemMap);
        yearItems.forEach(o->{
            List<QueryCbsPurchaseTotalResp.CbsStrategyItem> cbsStrategyItems = o.getCbsStrategyItems();
            cbsStrategyItems.forEach(k->{
                //除去部分配比
                BigDecimal cvmRatio=ratioMap.getOrDefault("cvm_ratio",BigDecimal.ONE);
                BigDecimal emptyRatio=BigDecimal.ONE.subtract(ratioMap.getOrDefault("empty_ratio",BigDecimal.ZERO));
                k.setPurchasePredictTotal(k.getPurchasePredictTotal().divide(cvmRatio,4, RoundingMode.HALF_UP).divide(emptyRatio,4, RoundingMode.HALF_UP));
                k.setPurchasePredictAboard(k.getPurchasePredictAboard().divide(cvmRatio,4, RoundingMode.HALF_UP).divide(emptyRatio,4, RoundingMode.HALF_UP));
                k.setPurchasePredictDomestic(k.getPurchasePredictDomestic().divide(cvmRatio,4, RoundingMode.HALF_UP).divide(emptyRatio,4, RoundingMode.HALF_UP));
            });
        });
        yearItems.sort(Comparator.comparingInt(QueryCbsPurchaseTotalResp.CbsYearItem::getYear));


        //7. 处理已完成部分
        //获取设备类型的配比
        Map<String, BigDecimal> deviceRatioMap = getDeviceRatioMap();


        //只处理当年
        QueryCbsPurchaseTotalResp.CbsYearItem currentYearItem = yearItems.get(0);
        String closingDate = params.get("finishEndDate").toString();
        String predictStartDate=params.get("finishStartDate").toString();
        List<CbsDemandMarketDTO> finishedList=cbsRatioService.getFinishedPurchaseData(longtermPredictTaskDO,params);
        finishedList.forEach(o->{
            BigDecimal deviceRatio = deviceRatioMap.getOrDefault(o.getDeviceType(),BigDecimal.ZERO);
            o.setUnit(o.getUnit().multiply(deviceRatio));
        });
        BigDecimal finishTotal= finishedList.stream()
                .map(CbsDemandMarketDTO::getUnit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal finishDomestic = finishedList.stream()
                .filter(o -> o.getCustomHouseTitle().equals("境内"))
                .map(CbsDemandMarketDTO::getUnit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal finishDomestic=finishTotal.multiply(BigDecimal.valueOf(0.75));
        BigDecimal finishAboard = finishedList.stream()
                .filter(o->o.getCustomHouseTitle().equals("境外"))
                .map(CbsDemandMarketDTO::getUnit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal finishAboard=finishTotal.multiply(BigDecimal.valueOf(0.25));
        BigDecimal finishRatio=BigDecimal.valueOf(1024);
        currentYearItem.getCbsStrategyItems().forEach(item->{
            item.setFinishedClosingDate(closingDate);
            item.setFinishedDomestic(finishDomestic.multiply(finishRatio));
            item.setFinishedAboard(finishAboard.multiply(finishRatio));
            item.setFinishedTotal(finishTotal. multiply(finishRatio));
            item.setFinishedRate(item.getFinishedTotal().divide(item.getPurchasePredictTotal(),4, RoundingMode.HALF_UP));
        });

        //8. 处理最新日期的采购已到货
        // 查询最新的采购到货量
        List<CbsDemandMarketDTO> latestPurchase = cbsRatioService.getLatestPurchaseData(longtermPredictTaskDO);
        latestPurchase.forEach(o->{
            BigDecimal deviceRatio = deviceRatioMap.getOrDefault(o.getDeviceType(),BigDecimal.ZERO);
            o.setUnit(o.getUnit().multiply(deviceRatio));
        });
        BigDecimal latestTotal= latestPurchase.stream()
                .map(CbsDemandMarketDTO::getUnit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal latestAboard = latestPurchase.stream()
                .filter(o->o.getCustomHouseTitle().equals("境外"))
                .map(CbsDemandMarketDTO::getUnit).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal latestDomestic = latestPurchase.stream()
                .filter(o->o.getCustomHouseTitle().equals("境内"))
                .map(CbsDemandMarketDTO::getUnit).reduce(BigDecimal.ZERO, BigDecimal::add);
        //按照一台物理机55tb进行计算
        BigDecimal latestRatio=BigDecimal.valueOf(1024);
        currentYearItem.getCbsStrategyItems().forEach(item->{
            item.setLatestClosingDate(latestPurchase.get(0).getDay());
            item.setLatestFinishedDomestic(latestDomestic.multiply(latestRatio));
            item.setLatestFinishedAboard(latestAboard.multiply(latestRatio));
            item.setLatestFinishedTotal(latestTotal.multiply(latestRatio));
            item.setLatestFinishedRate(item.getLatestFinishedTotal().divide(item.getPurchasePredictTotal(),4, RoundingMode.HALF_UP));
        });

        //9.调整境内外配比
        BigDecimal customhouseRatio = ratioMap.getOrDefault("customhouse_ratio", BigDecimal.ZERO);
        if(customhouseRatio==null||customhouseRatio.compareTo(BigDecimal.ZERO)==0)throw new RuntimeException("查询不到境内外配比");
        yearItems.forEach(yearItem->{
            yearItem.getCbsStrategyItems().forEach(item->{
                item.setPurchasePredictDomestic(item.getPurchasePredictTotal().multiply(customhouseRatio));
                item.setPurchasePredictAboard(item.getPurchasePredictTotal().multiply(BigDecimal.ONE.subtract(customhouseRatio)));
            });
        });

        //10. 提升当年/次年数据
        String currentYear = params.get("currentYear").toString();
        String nextYear = params.get("nextYear").toString();
        yearItems.forEach(yearitem->{
            int year = yearitem.getYear();
            if(year==Integer.parseInt(currentYear)){
                BigDecimal currentYearRatio = ratioMap.getOrDefault("current_enhance_year_ratio",BigDecimal.ONE);
                yearitem.getCbsStrategyItems().forEach(item->{
                    item.setPurchasePredictAboard(item.getPurchasePredictAboard().multiply(currentYearRatio));
                    item.setPurchasePredictDomestic(item.getPurchasePredictDomestic().multiply(currentYearRatio));
                    item.setPurchasePredictTotal(item.getPurchasePredictTotal().multiply(currentYearRatio));
                });
            }else if(year==Integer.parseInt(nextYear)){
               BigDecimal nextYearRatio = ratioMap.getOrDefault("next_enhance_year_ratio",BigDecimal.ONE);
                yearitem.getCbsStrategyItems().forEach(item->{
                    item.setPurchasePredictAboard(item.getPurchasePredictAboard().multiply(nextYearRatio));
                    item.setPurchasePredictDomestic(item.getPurchasePredictDomestic().multiply(nextYearRatio));
                    item.setPurchasePredictTotal(item.getPurchasePredictTotal().multiply(nextYearRatio));
                });
            }
        });
        resp.setCbsYearItems(yearItems);
        return resp;
    }

    private Map<String,BigDecimal> getDeviceRatioMap(){
        Map<String, BigDecimal> deviceRatioMap = new HashMap<>();
        deviceRatioMap.put("T0-CM6AX-100G", BigDecimal.valueOf(44));
        deviceRatioMap.put("T0-SW22A-100G", BigDecimal.valueOf(116));
        deviceRatioMap.put("X0-SH92XB-200G", BigDecimal.valueOf(66));
        deviceRatioMap.put("X0-SW32AF-200G", BigDecimal.valueOf(116));
        return deviceRatioMap;
    }

    /**
     * 半年累计
     * @param req
     * @return
     */
    @Override
    public QueryCbsPurchaseHalfYearAccumulateResp queryCbsPurchaseHalfYearAccumulate(QueryCbsPurchaseTotalReq req) {
        List<CbsRatioDetailDO> ratioDetailList = cdLabDbHelper.getAll(CbsRatioDetailDO.class, "where ratio_category_id=?", req.getRatioCategoryId());
        if(ratioDetailList==null||ratioDetailList.isEmpty())throw new RuntimeException("查询不到配比方案明细"+req.getRatioCategoryId()+"下的配比明细");
        Map<String, BigDecimal> ratioMap = cbsRatioService.getCommonRatioMap(req.getTaskId().toString());
        LongtermPredictTaskDO taskDO = cdLabDbHelper.getOne(LongtermPredictTaskDO.class, "where id = ?", req.getTaskId());
        int currentYear = taskDO.getPredictStart().getYear();
        int nextYear =currentYear+1;
        String sql="select strategy_type,year_month_str,sum(purchase_core) as predict_purchase_core,instance_type_from_device_type as cvm_instance_type,customhouse_title,SUBSTR(year_month_str,1,4) as year \n" +
                "from longterm_predict_output_purchase_split\n" +
                "where split_version_id=?\n" +
                "group by year_month_str,strategy_type,instance_type_from_device_type,customhouse_title\n" +
                "order by strategy_type,year_month_str,instance_type_from_device_type;";
        List<CbsPurchaseDTO> detail = cdLabDbHelper.getRaw(CbsPurchaseDTO.class, sql, req.getSplitVersionId());
        QueryCbsPurchaseHalfYearAccumulateResp resp=new QueryCbsPurchaseHalfYearAccumulateResp();
        BigDecimal currentYearRatio = ratioMap.getOrDefault("current_enhance_year_ratio",BigDecimal.ONE);
        BigDecimal nextYearRatio = ratioMap.getOrDefault("next_enhance_year_ratio",BigDecimal.ONE);
        detail.forEach(o->{
            if(o.getYear().equals(currentYear)){
                o.setPredictPurchaseCore(o.getPredictPurchaseCore().multiply(currentYearRatio));
            }else if(o.getYear().equals(nextYear)){
                o.setPredictPurchaseCore(o.getPredictPurchaseCore().multiply(nextYearRatio));
            }
        });
        //转换核心数
        convertSumCoreToSumDisk(req.getRatioCategoryId(),detail);
        mergeCbsLongtermPurchaseDTOAccumulate(detail);
        EStream.of(detail).groupAndConsume(CbsPurchaseDTO::getStrategyType,(strategyType, list)->{
            list.sort(Comparator.comparing(CbsPurchaseDTO::getYearMonthStr));
            String yearMonthStr = list.get(0).getYearMonthStr();
            list.removeIf(o->o.getYearMonthStr().equals(yearMonthStr));
            QueryCbsPurchaseHalfYearAccumulateResp.StrategyItem strategyItem=
                    new QueryCbsPurchaseHalfYearAccumulateResp.StrategyItem();
            resp.getStrategyItemList().add(strategyItem);
            strategyItem.setStrategyType(strategyType);
            Lang.list(6,12,18).forEach((num)->{
                List<CbsPurchaseDTO> curList= EStream.of(list).limit(num).toList();
                QueryCbsPurchaseHalfYearAccumulateResp.HalfYearAccumulateItem item =new HalfYearAccumulateItem();
                BigDecimal sum = EStream.of(list).limit(num).sum(CbsPurchaseDTO::getPredictPurchaseDisk);
                item.setPurchasePredictAccumulate(sum);
                item.setStartDate(curList.get(0).getYearMonthStr());
                item.setEndDate(curList.get(curList.size()-1).getYearMonthStr());
                strategyItem.getHalfYearAccumulateItems().add(item);
            });
            System.out.println(list);
        });
        //从cvm范围换算到全量
        resp.getStrategyItemList().forEach(k->{
            BigDecimal cvmRatio=ratioMap.getOrDefault("cvm_ratio",BigDecimal.ONE);
            BigDecimal emptyRatio=BigDecimal.ONE.subtract(ratioMap.getOrDefault("empty_ratio",BigDecimal.ZERO));
            k.getHalfYearAccumulateItems().forEach(j->{
                j.setPurchasePredictAccumulate(j.getPurchasePredictAccumulate()
                        .divide(cvmRatio,4, RoundingMode.HALF_UP)
                        .divide(emptyRatio,4, RoundingMode.HALF_UP));
            });
        });
        return resp;
    }


    public Map<Integer, Map<String, QueryCbsPurchaseTotalResp.CbsStrategyItem>> processStrategyItems(List<CbsPurchaseDTO> strategyItemList, Map<String, Object> params) {
        Map<Integer, Map<String,  QueryCbsPurchaseTotalResp.CbsStrategyItem>> strategyItemMap = new HashMap<>();
        for(CbsPurchaseDTO dto:strategyItemList){
            String strategyType = dto.getStrategyType();
            //对于特殊的存量数据特殊处理
            if(strategyType==null||strategyType.equals("存量"))continue;
            int predictYear=dto.getYear();
            String customhouseTitle = dto.getCustomhouseTitle();
            QueryCbsPurchaseTotalResp.CbsStrategyItem strategyItem=strategyItemMap
                    .computeIfAbsent(predictYear,k->new HashMap<>())
                    .computeIfAbsent(strategyType,k->new QueryCbsPurchaseTotalResp.CbsStrategyItem());

            BigDecimal purchase=dto.getPredictPurchaseDisk().add(dto.getFinishPurchaseDisk());
            strategyItem.setStrategyType(strategyType);
            populatePurchaseStrategyItem(strategyItem,customhouseTitle,purchase);
        }
        //针对一些境内外为空的情况进行修复
        strategyItemMap.forEach((k,v)->{
            v.forEach((m,strategyItem)->{
                if(strategyItem.getPurchasePredictTotal()==null)strategyItem.setPurchasePredictTotal(BigDecimal.ZERO);
                if(strategyItem.getPurchasePredictAboard()==null)strategyItem.setPurchasePredictAboard(BigDecimal.ZERO);
                if(strategyItem.getPurchasePredictDomestic()==null)strategyItem.setPurchasePredictDomestic(BigDecimal.ZERO);
            });
        });
        //特殊存量数据处理（目前有不属于amd/intel的存量数据，但是有境内外信息，需要在总量计算中添加。
        List<CbsPurchaseDTO> collect = strategyItemList.stream().filter(dto -> dto.getStrategyType().equals("存量")).collect(Collectors.toList());
        for(CbsPurchaseDTO dto:collect){
            String customhouseTitle = dto.getCustomhouseTitle();
            int year=dto.getYear();
            if(customhouseTitle.equals("境内")){
                strategyItemMap.get(year).forEach((k,v)->{
                    v.setPurchasePredictDomestic(v.getPurchasePredictDomestic().add(dto.getFinishPurchaseDisk().add(dto.getPredictPurchaseDisk())));
                    v.setPurchasePredictTotal((v.getPurchasePredictTotal().add(dto.getFinishPurchaseDisk().add(dto.getPredictPurchaseDisk()))));
                });
            }else {
                strategyItemMap.get(year).forEach((k,v)->{
                    v.setPurchasePredictAboard(v.getPurchasePredictAboard().add(dto.getFinishPurchaseDisk().add(dto.getPredictPurchaseDisk())));
                    v.setPurchasePredictTotal((v.getPurchasePredictTotal().add(dto.getFinishPurchaseDisk().add(dto.getPredictPurchaseDisk()))));
                });
            }
        }
        return strategyItemMap;

    }

    private void populatePurchaseStrategyItem(QueryCbsPurchaseTotalResp.CbsStrategyItem strategyItem, String customhouseTitle, BigDecimal purchase) {
        // 填充当年净增规模预测
        strategyItem.setPurchasePredictTotal(strategyItem.getPurchasePredictTotal() == null ? purchase : strategyItem.getPurchasePredictTotal().add(purchase));
        if (customhouseTitle.equals("境内")) {
            strategyItem.setPurchasePredictDomestic(strategyItem.getPurchasePredictDomestic() == null ? purchase : strategyItem.getPurchasePredictDomestic().add(purchase));
        } else if (customhouseTitle.equals("境外")) {
            strategyItem.setPurchasePredictAboard(strategyItem.getPurchasePredictAboard() == null ? purchase : strategyItem.getPurchasePredictAboard().add(purchase));
        }

    }

    /**
     * 合并并计算预测和已完成的采购量
     * @param cbsPredictDTOList 预测数据列表
     * @param cbsFinishDTOList 完成数据列表
     * @return 合并并计算后的DTO列表
     */
    public List<CbsPurchaseDTO> mergeAndCalculate(
            List<CbsPurchaseDTO> cbsPredictDTOList,
            List<CbsPurchaseDTO> cbsFinishDTOList) {

        Map<String, CbsPurchaseDTO> finishMap = new HashMap<>();
        Map<String, CbsPurchaseDTO> predictMap = new HashMap<>();
        // 处理完成数据
        for (CbsPurchaseDTO finishDTO : cbsFinishDTOList) {
            String keyf = generateFinishKey(finishDTO);  // 使用没有策略的key生成方法

            //特殊处理机型为默认的值
            if(finishDTO.getCvmInstanceType().equals("(空值)")){
                if(!predictMap.containsKey(keyf)){
                predictMap.put(keyf, finishDTO);}
                else{
                    CbsPurchaseDTO dto=predictMap.get(keyf);
                    dto.setFinishPurchaseCore(dto.getFinishPurchaseCore().add(finishDTO.getFinishPurchaseCore()));
                }
                continue;
            }

            if(!finishMap.containsKey(keyf)){finishMap.put(keyf, finishDTO);continue;}
            CbsPurchaseDTO existingDTO = finishMap.get(keyf);
            existingDTO.setFinishPurchaseCore(existingDTO.getFinishPurchaseCore().add(finishDTO.getFinishPurchaseCore()));


        }

        // 处理预测数据
        for (CbsPurchaseDTO predictDTO : cbsPredictDTOList) {
            String keyp = generatePredictKey(predictDTO);  // 使用策略字段生成key
            if(!predictMap.containsKey(keyp)){
                String keyf = generateFinishKey(predictDTO);  // 使用没有策略的key生成方法
                if(finishMap.containsKey(keyf))predictDTO.setFinishPurchaseCore(finishMap.get(keyf).getFinishPurchaseCore());
                predictMap.put(keyp, predictDTO);
            }else{
                CbsPurchaseDTO existingDTO = predictMap.get(keyp);
                existingDTO.setPredictPurchaseCore(existingDTO.getPredictPurchaseCore().add(predictDTO.getPredictPurchaseCore()));
            }
        }

        // 检查那些只存在于finishMap中的机型，并将其添加到predictMap
        for (Map.Entry<String, CbsPurchaseDTO> entry : finishMap.entrySet()) {
            String keyf = entry.getKey();
            CbsPurchaseDTO finishDTO = entry.getValue();

            // 查找finishDTO对应的预测数据key
            boolean foundMatchingPredictKey = false;

            // 遍历predictMap，找到与finishMap中的机型相匹配的key
            for (CbsPurchaseDTO predictDTO : predictMap.values()) {
                // 如果finishMap中的key与predictMap中的策略类型及其他信息匹配，重新生成predict的key
                String newPredictKey = generatePredictKey(predictDTO); // 使用已有的策略类型生成新的key
                if (keyf.equals(generateFinishKey(predictDTO))) { // 检查匹配
                    foundMatchingPredictKey = true;
                    break;
                }
            }

            // 如果没有找到匹配的策略，仍将其加到predictMap中，并设置预测量为0
            if (!foundMatchingPredictKey) {
                String strategyType = "存量"; // 默认策略，或根据需要从预测中提取策略
                finishDTO.setPredictPurchaseCore(BigDecimal.ZERO); // 预测量默认为0
                finishDTO.setStrategyType(strategyType);
                predictMap.put(keyf, finishDTO);
            }
        }

        return new ArrayList<>(predictMap.values());
    }

    /**
     * 合并同类dto
     * @param dtoList
     */
    public void mergeCbsLongtermPurchaseDTOAccumulate(List<CbsPurchaseDTO> dtoList){
        // 合并相同年月、相同策略类型的DTO
        Map<String, CbsPurchaseDTO> mergedDtoMap = new HashMap<>();

        for (CbsPurchaseDTO dto : dtoList) {
            // 生成一个唯一的键，根据年月+策略类型来合并
            String key = dto.getYearMonthStr() + "-" +
                    //dto.getCbsInstanceFamily() + "-" +
                    //dto.getCustomhouseTitle() + "-" +
                    dto.getStrategyType();
            dto.setCustomhouseTitle(null);
            dto.setCbsInstanceFamily(null);
            // 如果该组合还未出现，直接加入Map
            if (!mergedDtoMap.containsKey(key)) {
                mergedDtoMap.put(key, dto);
            } else {
                // 如果已经存在，累加sumCore和sumDisk
                CbsPurchaseDTO existingDto = mergedDtoMap.get(key);
                existingDto.setPredictPurchaseCore(existingDto.getPredictPurchaseCore().add(dto.getPredictPurchaseCore()));
                existingDto.setPredictPurchaseDisk(existingDto.getPredictPurchaseDisk().add(dto.getPredictPurchaseDisk()));
            }
        }

        // 将合并后的DTO列表设置回原始列表
        dtoList.clear();
        dtoList.addAll(mergedDtoMap.values());
    }


    /**
     * 查询预测部分CVM的采购量，用于转化
     * @return
     */
    @SneakyThrows
    private List<CbsPurchaseDTO> queryCvmPurchasePredictDTO(Map<String, Object> params){
        String sql = IOUtils.readClasspathResourceAsString("sql/longterm_predict/cbs/cbs_purchase_predict_total.sql");
        List<CbsPurchaseDTO> all = cdLabDbHelper.getRaw(CbsPurchaseDTO.class, sql, params);
        if(all==null||all.isEmpty())throw new RuntimeException("查询预测采购量为空");
        return all;
    }

    /**
     * 查询cvm已经完成的采购量
     * @param params
     * @return
     */
    @SneakyThrows
    private List<CbsPurchaseDTO> queryCvmPurchaseFinishDTO(Map<String, Object> params) {
        String sql = IOUtils.readClasspathResourceAsString("sql/longterm_predict/cbs/cbs_purchase_finish_total.sql");
        List<CbsPurchaseDTO> all = cdLabDbHelper.getRaw(CbsPurchaseDTO.class, sql, params);
        if(all==null||all.isEmpty())throw new RuntimeException("查询预测采购量为空");
        return all;
    }

    /**
     * 获取查询的参数
     * @param req
     * @param taskDO
     * @return
     */
    private Map<String, Object> getPurchaseParams(QueryCbsPurchaseTotalReq req,
                                                  LongtermPredictTaskDO taskDO) {
        LocalDate predictStart = taskDO.getPredictStart();
        int currentYear = taskDO.getPredictStart().getYear();
        int nextYear = currentYear + 1;
        String yearMonthStr = currentYear+"-%";
//        String predStartDate = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue()).toString();
//        String predEndDate = YearMonth.of(predictStart.getYear() , 12).plusYears(1).toString();
        String finishEndDate = predictStart.minusMonths(1).withDayOfMonth(predictStart.minusMonths(1).lengthOfMonth()).toString();
        String finishStartDate = YearMonth.of(predictStart.getYear(), 1).toString()+"-01";
//        String finishEndDate = YearMonth.of(predictStart.getYear(), predictStart.getMonthValue())
//                .minusMonths(1).toString();

        // 当年的采购量， 当年的预测量，明年的预测量
        Map<String, Object> params = new HashMap<>();
        params.put("splitVersionId", req.getSplitVersionId());
        params.put("taskId", req.getTaskId());
        params.put("currentYear", currentYear);
        params.put("nextYear", nextYear);
        params.put("yearMonthStr", yearMonthStr);
        params.put("finishStartDate", finishStartDate);
        params.put("finishEndDate", finishEndDate);
//        params.put("predStartDate", predStartDate);
//        params.put("predEndDate", predEndDate);
        return params;
    }

    /**
     * 生成预测数据的唯一key（基于customhouseTitle, year, strategyType, instanceType）
     * @param dto DTO
     * @return 生成的唯一key
     */
    private String generatePredictKey(CbsPurchaseDTO dto) {
        return dto.getCustomhouseTitle() + "-" + dto.getYear() + "-" + dto.getStrategyType() + "-" + dto.getCvmInstanceType();
    }

    /**
     * 生成完成数据的唯一key（基于customhouseTitle, year, instanceType）
     * @param dto DTO
     * @return 生成的唯一key
     */
    private String generateFinishKey(CbsPurchaseDTO dto) {
        return dto.getCustomhouseTitle() + "-" + dto.getYear() + "-" + dto.getCvmInstanceType();
    }


    /**
     * 筛选出符合条件的配比条目
     * @param dto
     * @param ratioDetailList
     * @return
     */
    private List<CbsRatioDetailDO> filterMatchingRatios(CbsPurchaseDTO dto,
                                                        List<CbsRatioDetailDO> ratioDetailList) {
        List<CbsRatioDetailDO> validRatios = new ArrayList<>();

        // 遍历每个配比条目
        for (CbsRatioDetailDO ratio : ratioDetailList) {
            boolean matches = true;


            // 检查每个字段的匹配条件，优先匹配有值的字段
            if (!"(默认)".equals(ratio.getCbsInstanceFamily()) && !ratio.getCbsInstanceFamily().equals(dto.getCvmInstanceType())) {
                matches = false;
            }
            if (!"(默认)".equals(ratio.getCustomhouseTitle()) && !ratio.getCustomhouseTitle().equals(dto.getCustomhouseTitle())) {
                matches = false;
            }
            if (!"(默认)".equals(ratio.getStrategyType()) && !ratio.getStrategyType().equals(StrategyTypeEnum.getNameByCode(dto.getStrategyType()))) {
                matches = false;
            }
            // 如果 cbsInstanceFamily 是 null，匹配 "默认"
            if ((dto.getCvmInstanceType() == null || dto.getCvmInstanceType().equals("(空值)"))&& "默认".equals(ratio.getCbsInstanceFamily())) {
                matches = true;
            }

            // 如果匹配，添加到有效配比列表
            if (matches) {
                validRatios.add(ratio);
            }
        }
        // 排序逻辑：优先选择没有 "(默认)" 的条目，若有多个 "(默认)"，选择字段使用 "(默认)" 最少的条目
        validRatios.sort((ratio1, ratio2) -> {
            int defaultCount1 = countDefaultFields(ratio1);
            int defaultCount2 = countDefaultFields(ratio2);
            return Integer.compare(defaultCount1, defaultCount2); // 默认字段越少，优先级越高
        });

        return validRatios;
    }

    /**
     * 计算一个配比条目中有多少个字段是 "(默认)"
     * @param ratio
     * @return 默认字段的数量
     */
    private int countDefaultFields(CbsRatioDetailDO ratio) {
        int defaultCount = 0;

        if ("(默认)".equals(ratio.getCbsInstanceFamily())) {
            defaultCount++;
        }
        if ("(默认)".equals(ratio.getCustomhouseTitle())) {
            defaultCount++;
        }
        if ("(默认)".equals(ratio.getBizRangeType())) {
            defaultCount++;
        }
        if ("(默认)".equals(ratio.getStrategyType())) {
            defaultCount++;
        }

        return defaultCount;
    }

    /**
     * 将CbsLongtermPredictScaleDTO中的sumCore转换为sumDisk
     * @param cbsPredictDTOList
     * @return
     */
    public List<CbsPurchaseDTO> convertSumCoreToSumDisk(
            Long categoryId,
            List<CbsPurchaseDTO> cbsPredictDTOList) {
        List<CbsRatioDetailDO> cbsRatioDetailDOS = cbsRatioService.buildCvmInstanceRatioList(categoryId);
        List<CbsPurchaseDTO> result=new ArrayList<>(cbsPredictDTOList);
        for (CbsPurchaseDTO dto:result){
                //BigDecimal ratio = cvmInstanceRatioMap.get(dto.getCvmInstanceType());
                List<CbsRatioDetailDO> validRatios = filterMatchingRatios(dto, cbsRatioDetailDOS);
                BigDecimal ratio=BigDecimal.ZERO;
                if(!validRatios.isEmpty()){
                    ratio=validRatios.get(0).getRatioNum();
                    dto.setPredictPurchaseDisk(dto.getPredictPurchaseCore().multiply(ratio));
                    if(dto.getFinishPurchaseCore()!=null)dto.setFinishPurchaseDisk(dto.getFinishPurchaseCore().multiply(ratio));
                }else{
                    ratio=BigDecimal.valueOf(0);
                    log.warn("没有找到对应的cvmInstanceType："+dto.getCvmInstanceType()+"请尝试设置默认配比");
                    dto.setPredictPurchaseDisk(dto.getPredictPurchaseCore().multiply(ratio));
                    dto.setFinishPurchaseDisk(dto.getFinishPurchaseCore().multiply(ratio));
                }

        }
        return result;

    }

//    public List<CbsPurchaseDTO> convertSumCoreFinishToSumDiskFinish(
//            List<CbsPurchaseDTO> cbsLongtermPredictScaleDTOList,
//            List<CbsRatioDetailDO> ratioDetailList
//    ){
//        for (CbsPurchaseDTO dto : cbsLongtermPredictScaleDTOList) {
//            BigDecimal sumCoreFinish = dto.getFinishPurchaseCore();
//            BigDecimal sumDiskFinish = BigDecimal.ZERO;
//
//            // 逐条对比，筛选出符合条件的配比条目
//            List<CbsRatioDetailDO> validRatios = filterMatchingRatios(dto, ratioDetailList);
//
//            // 如果只剩下一个有效的配比条目，使用该条配比计算sumDisk
//            if (!validRatios.isEmpty()) {
//                sumDiskFinish = sumCoreFinish.multiply(validRatios.get(0).getRatioNum());
//            } else {
//                // 如果只剩下"默认"条目，使用默认配比值
//                BigDecimal defaultRatio = BigDecimal.valueOf(40);
//                sumDiskFinish = sumCoreFinish.multiply(defaultRatio);
//            }
//
//            dto.setFinishPurchaseDisk(sumDiskFinish);
//        }
//
//        return cbsLongtermPredictScaleDTOList;
//    }
    /**
     * 查找默认的配比值
     * @param ratioDetailList
     * @return
     */
    private BigDecimal findDefaultRatio(List<CbsRatioDetailDO> ratioDetailList) {
        for (CbsRatioDetailDO ratio : ratioDetailList) {
            if ("默认".equals(ratio.getCbsInstanceFamily())) {
                return ratio.getRatioNum();
            }
        }
        //现在没有确定的默认配比
        return new BigDecimal("40"); // 如果没有找到"默认"条目，使用固定的默认配比值（如20%）
    }
    /**
     * 构建年数据
     * @param strategyItemMap
     * @return
     */
    public List<QueryCbsPurchaseTotalResp.CbsYearItem> buildYearItems(Map<Integer, Map<String, QueryCbsPurchaseTotalResp.CbsStrategyItem>> strategyItemMap) {
        List<QueryCbsPurchaseTotalResp.CbsYearItem> yearItems = new ArrayList<>();
        for (Map.Entry<Integer, Map<String, QueryCbsPurchaseTotalResp.CbsStrategyItem>> yearEntry : strategyItemMap.entrySet()) {
            QueryCbsPurchaseTotalResp.CbsYearItem yearItem = new QueryCbsPurchaseTotalResp.CbsYearItem();
            yearItem.setYear(yearEntry.getKey()); // 使用预测年份
            yearItem.setCbsStrategyItems(new ArrayList<>(yearEntry.getValue().values()));
            yearItems.add(yearItem);
        }
        return yearItems;
    }
}
