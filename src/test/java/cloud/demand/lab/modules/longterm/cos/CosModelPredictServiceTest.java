package cloud.demand.lab.modules.longterm.cos;

import cloud.demand.lab.modules.longterm.cos.service.CosModelPredictService;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryPredictResultReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryPredictResultResp;
import com.alibaba.fastjson2.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class CosModelPredictServiceTest {

    @Resource
    private CosModelPredictService cosModelPredictService;

    @Test
    public void testQueryPredictResult() {
        QueryPredictResultReq req = new QueryPredictResultReq();
        req.setCategoryId(1L); // 使用一个测试的categoryId
        req.setTaskId(0L);

        try {
            QueryPredictResultResp resp = cosModelPredictService.queryPredictResult(req);
            
            System.out.println("=== 预测结果 ===");
            System.out.println("线数量: " + (resp.getLines() != null ? resp.getLines().size() : 0));
            
            if (resp.getLines() != null) {
                for (QueryPredictResultResp.Line line : resp.getLines()) {
                    System.out.println("\n--- 线信息 ---");
                    System.out.println("范围: " + line.getScope());
                    System.out.println("类型: " + line.getType());
                    System.out.println("算法: " + line.getAlgorithm());
                    System.out.println("数据点数量: " + (line.getPoints() != null ? line.getPoints().size() : 0));
                    System.out.println("增速信息数量: " + (line.getIncreaseInfos() != null ? line.getIncreaseInfos().size() : 0));
                    
                    if (line.getIncreaseInfos() != null && !line.getIncreaseInfos().isEmpty()) {
                        System.out.println("增速详情:");
                        for (QueryPredictResultResp.IncreaseInfo increaseInfo : line.getIncreaseInfos()) {
                            System.out.println("  - " + increaseInfo.getDateName() + 
                                             ": " + increaseInfo.getRate() + 
                                             " (" + increaseInfo.getStartDate() + " ~ " + increaseInfo.getEndDate() + ")");
                        }
                    }
                }
            }
            
            System.out.println("\n=== JSON输出 ===");
            System.out.println(JSON.toJSONString(resp));
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
